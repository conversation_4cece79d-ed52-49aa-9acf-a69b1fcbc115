*** ShipStation for WooCommerce ***

= 4.7.6 - 2025-08-11 =
* Tweak - WooCommerce 10.1 compatibility.

= 4.7.5 - 2025-08-05 =
* Fix   - Out of memory allocation error on checkout page.
* Fix   - Remove deprecated load_plugin_textdomain() call.

= 4.7.4 - 2025-07-07 =
* Tweak - WooCommerce 10.0 compatibility.

= 4.7.3 - 2025-06-30 =
* Fix   - Compatibility issue with WooCommerce version lower than 8.9.

= 4.7.2 - 2025-06-24 =
* Fix   - Fatal error on Checkout page.

= 4.7.1 - 2025-06-18 =
* Fix   - Fatal error on WooCommerce Subscriptions edit page.

= 4.7.0 - 2025-06-17 =
* Add   - REST API endpoints to update and retrieve product inventory data.
* Add   - Gift feature.

= 4.6.1 - 2025-06-09 =
* Tweak - WooCommerce 9.9 compatibility.

= 4.6.0 - 2025-06-02 =
* Add   - New hook `woocommerce_shipstation_shipnotify_status_updated` that will be called after the order status is changed.
* Add   - REST API endpoints to update and retrieve product inventory data.

= 4.5.2 - 2025-05-26 =
* Fix   - Security updates.
* Tweak - Update ShipStation branding.

= 4.5.1 - 2025-04-22 =
* Add   - Include the product dimensions when exporting an order to ShipStation.
* Tweak - Added a filter to allow the user to disable exporting order discounts as a separate line item to ShipStation.

= 4.5.0 - 2025-04-14 =
* Add   - woocommerce_shipstation_shipnotify_order_shipped filter - Allow to override is order shipped.
* Add   - woocommerce_shipstation_shipnotify_tracking_note filter - Allow to override tracking note.
* Add   - woocommerce_shipstation_shipnotify_send_tracking_note filter - Allow to override should tracking note be sent to customer.
* Tweak - Move woocommerce_shipstation_shipnotify action before order status is updated.

= 4.4.9 - 2025-04-07 =
* Tweak - WooCommerce 9.8 compatibility.

= 4.4.8 - 2025-03-10 =
* Fix   - Make the value of `woocommerce_shipstation_get_order_id` filter consistent by removing the conversion function.

= 4.4.7 - 2025-03-04 =
* Tweak - PHP 8.4 Compatibility.
* Tweak - WooCommerce 9.7 Compatibility.

= 4.4.6 - 2024-11-27 =
* Tweak - Reimplemented compatibility with WordPress 6.7 while maintaining unchanged execution priorities.

= 4.4.5 - 2024-10-28 =
* Tweak - WordPress 6.7 Compatibility.

= 4.4.4 - 2024-07-02 =
* Fix   - Security updates.
* Tweak - WooCommerce 9.0 and WordPress 6.6 Compatibility.

= 4.4.3 - 2024-05-27 =
* Tweak - Performance enhancement.

= 4.4.2 - 2024-04-09 =
* Fix - Cannot retrieve order number on from GET variable.

= 4.4.1 - 2024-03-25 =
* Tweak - WordPress 6.5 compatibility.

= 4.4.0 - 2024-03-19 =
* Fix - Applying WordPress coding standards.

= 4.3.9 - 2023-09-05 =
* Fix - Security updates.
* Tweaks - Developer dependencies update.
* Add - Developer QIT workflow.

= 4.3.8 - 2023-08-09 =
* Fix - Security updates.

= 4.3.7 - 2023-05-08 =
* Fix - Allow filtering the order exchange rate and currency code before exporting to ShipStation.

= 4.3.6 - 2023-04-20 =
* Fix - Compatibility for Sequential Order Numbers by WebToffee.
* Add - New query var for WC_Order_Query called `wt_order_number` to search order number.

= 4.3.5 - 2023-04-17 =
* Fix - Revert version 4.3.4's compatibility update for Sequential Order Numbers by WebToffee.

= 4.3.4 - 2023-04-12 =
* Fix - Compatibility for Sequential Order Numbers by WebToffee.

= 4.3.3 - 2023-03-29 =
* Fix - Fatal error when product image does not exist.

= 4.3.2 - 2022-11-29 =
* Fix - Use product variation name when exporting a product variation.

= 4.3.1 - 2022-10-25 =
* Add - Declared HPOS compatibility.

= 4.3.0 - 2022-10-13 =
* Add - High-Performance Order Storage compatibility.

= 4.2.0 - 2022-09-07 =
* Add   - Filter for manipulating address export data.
* Fix   - Remove unnecessary files from plugin zip file.
* Tweak - Transition version numbering to WordPress versioning.
* Tweak - WC 6.8 and WP 6.0 compatibility.
* Fix - Remove 'translate : true' in package.json.

= 4.1.48 - 2021-11-03 =
* Fix - Critical Error when null value is passed to appendChild method.
* Fix - $logging_enabled compared against string instead of boolean.

= 4.1.47 - 2021-09-29 =
* Fix - Change API Export order search to be accurate down to the second, not just the date.

= 4.1.46 - 2021-09-10 =
* Fix   - Order is not changed to completed when the order has partial refund and is marked as shipped in ShipStation.

= 4.1.45 - 2021-08-24 =
* Fix   - Remove all usage of deprecated $HTTP_RAW_POST_DATA.

= 4.1.44 - 2021-08-12 =
* Fix   - Changing text domain to "woocommerce-shipstation-integration" to match with plugin slug.
* Fix   - Order product quantities do not sync to Shipstation when using a refund.
* Fix   - PHP notice error "wc_cog_order_total_cost" was called incorrectly.

= 4.1.43 - 2021-07-27 =
* Fix   - API returns status code 200 even when errors exist.
* Tweak - Add version compare for deprecated Order::get_product_from_item().

= 4.1.42 - 2021-04-20 =
* Fix - Use order currency code instead of store currency.

= 4.1.41 - 2021-03-02 =
* Add - Add currency code and weight units to orders XML.

= 4.1.40 - 2020-11-24 =
* Tweak - PHP 8 compatibility fixes.

= 4.1.39 - 2020-10-06 =
* Add   - Add woocommerce_shipstation_export_order_xml filter.
* Tweak - Update Readme.
* Tweak - WC 4.5 compatibility.
* Fix   - Updated shop_thumbnail to woocommerce_gallery_thumbnail for thumbnail export.

= 4.1.38 - 2020-08-19 =
* Tweak - WordPress 5.5 compatibility.

= 4.1.37 - 2020-06-05 =
* Tweak - Dismiss activation notice independent of user.

= 4.1.36 - 2020-04-29 =
* Tweak - WC 4.1 compatibility.

= 4.1.35 - 2020-04-21 =
* Tweak - Obfuscate logging data.

= 4.1.34 2020-03-09 =
* Tweak - WP tested up to 5.4.
* Tweak - WC tested up to 4.0.

= 4.1.33 2020-03-04 =
* Tweak - Use code sniff version.
* Tweak - WC 4.0 compatibility.

= 4.1.32 2020-02-12 =
* Fix - Export shipping address even when shipping country is not available.

= 4.1.31 2020-01-15 =
* Tweak - WP 5.3 compatibility.
* Add   - Filter `woocommerce_shipstation_no_shipping_item` for when an item does not need shipping or is a fee.

= 4.1.30 2019-11-04 =
* Tweak - WC 3.8 compatibility.

= 4.1.29 2019-08-12 =
* Tweak - WC 3.7 compatibility.

= 4.1.28 2019-04-17 =
* Tweak - WC 3.6 compatibility.

= 4.1.27 2019-01-07 =
* Fix - Use product name from order instead of product itself.
* Fix - Prevent errors when WooCommerce isn't active.

= 4.1.26 2018-12-10 =
* Update - Setup notice link to WooCommerce-specific landing page.

= 4.1.25 2018-11-08 =
* Update - WP 5.0 compatibility.

= 4.1.24 2018-10-18 =
* Fix - Add missing language .pot file.
* Update - WC 3.5 compatibility.

= 4.1.23 2018-09-12 =
* Fix    - Use correct textdomain on some strings.
* Tweak  - Rework settings notice to correctly provide setup instructions.
* Tweak  - Coding standards and making the plugin ready for wordpress.org.

= 4.1.22 2018-05-24 =
* Fix    - Order timestamp issue.

= 4.1.21 2018-05-23 =
* Fix    - Privacy policy updates.

= 4.1.20 2018-05-23 =
* Fix    - Paid date not showing actual payment date, but Order Date instead.
* Update - Privacy policy notification.
* Update - Export/erasure hooks added.
* Update - WC 3.4 compatibility.

= 4.1.19 2017-12-15 =
* Fix - WC 3.3 compatibility.

= 4.1.18 2017-07-18 =
* Fix - Update the order status to complete if XML from ShipStation is not present in request's body. Also log the request information.
* Fix - Adjusted text domain for two strings so that they are now translateable.

= 4.1.17 2017-07-06 =
* Fix - Issue when a server couldn't read ShipNotify's XML posted in request's body, nothing is updated in the order.
* Tweak - Added setting, docs, and support links in plugin action links.

= 4.1.16 2017-06-14 =
* Fix - Issue where legacy code for converting sequential order numbers still used.
* Fix - Make sure to not count non shippable item when get notified from ShipStation.

= 4.1.15 2017-05-12 =
* Fix - Ensure some orders from previous version of ShipStation are able to be found on notifications.

= 4.1.14 2017-05-11 =
* Fix - Possible error when order is not found during shipment notification.
* Tweak - Order numbers are now sent via own XML field and will not display in invoice.

= 4.1.13 2017-05-05 =
* Fix - WC30 date/time not displaying correctly.
* Fix - Tax amount discrenpancy when sent to Shipstation.
* Fix - When using split orders, order does not get updated in WooCommerce.
* Tweak - Sequential Numbers Pro compatibility.
* Add - Exported order note when the order has been exported.

= 4.1.12 2017-05-02 =
* Fix - Product attributes not passing to Shipstation under certain conditions.

= 4.1.11 2017-05-01 =
* Fix - Export error due to WC30 incompatibility.

= 4.1.10 2017-04-10 =
* Fix  - Allow additional characters to be used for shipping service name

= 4.1.9 2017-04-06 =
* Fix  - Additional updates for WC 3.0 compatibility

= 4.1.8 2017-04-03 =
* Fix  - PHP 7 compatibility
* Fix  - Update for WC 3.0 compatibility

= 4.1.7 2016-10-03 =
* Fix  - Digital products are also sent through.
* Fix  - Checkout add on fee not being sent through.

= 4.1.6 2016-08-15 =
* Tweak - Added filter for ShipNotify order ID
* Tweak - Send payment method ShipStation
* Fix   - Issue where fee items not be exported to ShipStation

= 4.1.5 2016-02-24 =
* Fix   - Compatibility issue with WC Order Status Manager

= 4.1.4 2016-01-25 =
* Fix   - Compatibility issue with woocommerce-sequential-order-numbers-pro version 1-9-0

= 4.1.3 2015-09-23 =
* Fix   - Allow copy/paste from API key field in firefox

= 4.1.2 2015-08-21 =
* Fix   - Send pre-discount unit price.

= 4.1.1 2015-08-06 =
* Fix   - Send UnitPrice as single product total-
* Tweak - Date parsing.

= 4.1.0 2015-06-24 =
* Fix   - Sanitize XML response.
* Fix   - Prevent API requests being callable when not authenticated.
* Fix   - Prevent caching.
* Tweak - Use hash_equals to compare keys.
* Tweak - Send total discount to ShipStation.

= 4.0.9 2015-05-12 =
* Tweak - woocommerce_shipstation_export_order filter.
* Tweak - Exclude system notes.
* Tweak - Custom field value filters.

= 4.0.8 2015-04-03 =
* Fix   - Don't automatically set to $is_customer_note to true

= 4.0.7 2015-03-12 =
* Check if $product exists before checking if needs_shipping in export.

= 4.0.6 2015-01-16 =
* Send negative discount.

= 4.0.5 2015-01-08 =
* Export query based on post_modified_gmt rather than post_date_gmt

= 4.0.4 2014-11-19 =
* Fix compatibility with Sequential order numbers.

= 4.0.3 2014-11-13 =
* Extra logging in ShipNotify.
* Fixed completing orders with multiple lines.

= 4.0.2 2014-11-13 =
* Order results by date.
* Enforce minimum page 1.
* Removed check to see if orders need shipping to prevent issues with offset/max pages. Exports all orders.

= 4.0.1 2014-11-12 =
* Added 'pages' node to XML feed so ShipStation knows how many pages of results are present.

= 4.0.0 2014-11-01 =
* Completely refactored by WooThemes!
* Supports split orders (only completes the order once all items are shipped).
* Exports orders (from statuses you define).
* Excludes orders and items which do not require shipping.
* Simplified setup process; just requires an auth key.
* Exports order-level discounts as line items.
