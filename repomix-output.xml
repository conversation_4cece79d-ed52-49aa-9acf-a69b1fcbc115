This file is a merged representation of a subset of the codebase, containing files not matching ignore patterns, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching these patterns are excluded: languages/, .phpstan/, .github/
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
assets/
  images/
    shipstation-logo.svg
  js/
    checkout.js
  sass/
    admin.scss
    block-checkout.scss
    classic-checkout.scss
includes/
  api/
    docs/
      inventory-api.md
      README.md
    requests/
      class-wc-safe-domdocument.php
      class-wc-shipstation-api-export.php
      class-wc-shipstation-api-request.php
      class-wc-shipstation-api-shipnotify.php
    rest/
      class-inventory-controller.php
  data/
    data-settings.php
  class-checkout.php
  class-rest-api-loader.php
  class-wc-shipstation-api.php
  class-wc-shipstation-integration.php
  class-wc-shipstation-privacy.php
  trait-woocommerce-order-util.php
.gitignore
.nvmrc
.phpcs.security.xml
changelog.txt
composer.json
package.json
README.md
readme.txt
woocommerce-shipstation.php
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="assets/images/shipstation-logo.svg">
<svg width="260" height="40" viewBox="0 0 260 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M40.7763 16.9336C37.8384 16.9336 35.4564 14.5971 35.4564 11.7152C35.4564 10.2751 36.0523 8.97053 37.0128 8.02678L32.5934 3.69173C31.6313 4.63548 30.3013 5.21839 28.8332 5.21839C25.8953 5.21839 23.5133 2.88187 23.5133 0H17.2646C17.2646 2.8835 14.8827 5.22002 11.9448 5.22002C10.4766 5.22002 9.14666 4.63548 8.18456 3.69336L3.7652 8.02841C4.7273 8.97216 5.32155 10.2768 5.32155 11.7169C5.32155 14.5987 2.93958 16.9353 0.00166454 16.9353L0 23.0664C2.93792 23.0664 5.32155 25.4029 5.32155 28.2848C5.32155 29.7249 4.72564 31.0295 3.7652 31.9732L8.18456 36.3083C9.14666 35.3645 10.4766 34.7816 11.9448 34.7816C14.8827 34.7816 17.2646 37.1181 17.2646 40H23.5133C23.5133 37.1165 25.8953 34.78 28.8332 34.78C30.3013 34.78 31.6313 35.3629 32.5934 36.3066L37.0128 31.9716C36.0507 31.0278 35.4564 29.7232 35.4564 28.2831C35.4564 25.4013 37.8384 23.0647 40.7763 23.0647V16.9336ZM30.6276 25.7997L20.3873 31.5993L10.1471 25.7997V14.2003L20.3873 8.40069L30.6276 14.2003V25.7997Z" fill="#003311"/>
<path d="M63.5972 6.58993C67.8501 6.58993 70.8513 9.01135 72.4076 11.9912L68.724 14.1154C67.2043 11.9177 65.7994 10.9119 63.6355 10.9119C61.395 10.9119 59.9136 12.1038 59.9136 13.8917C59.9136 15.2698 61.0155 16.3507 63.1794 17.1328L65.0021 17.8039C70.7364 19.7779 73.1284 22.0508 73.1284 25.6641C73.1284 28.0856 72.1796 29.9845 70.2803 31.3642C68.3811 32.7423 66.1789 33.4133 63.7104 33.4133C61.3934 33.4133 59.3443 32.8174 57.5582 31.6254C55.8121 30.4335 54.6336 28.9068 54.0277 27.0063L57.8629 25.0322C59.0397 27.6774 61.0155 28.9803 63.7104 28.9803C66.2555 28.9803 67.965 27.7884 67.965 25.5907C67.965 23.7652 66.863 23.0207 63.6355 21.8287L61.9643 21.1952C57.4451 19.5934 54.9765 17.5818 54.9765 13.7448C54.9765 11.5846 55.8121 9.87019 57.4451 8.56723C59.078 7.26263 61.1287 6.59319 63.5972 6.59319V6.58993Z" fill="#003311"/>
<path d="M87.7098 13.2958C89.7222 13.2958 91.4317 14.0044 92.8749 15.456C94.3563 16.9091 95.0771 19.0318 95.0771 21.863V33.0378H90.1783V22.1602C90.1783 19.0318 88.3556 17.5035 85.9254 17.5035C84.5954 17.5035 83.4569 17.9884 82.5847 18.9191C81.7108 19.8498 81.2181 20.7446 81.1781 21.6018V33.0378H76.2794V6.70259H81.1781V17.4675C81.4444 16.6103 82.1652 15.7172 83.3437 14.749C84.5205 13.7807 85.962 13.2958 87.7098 13.2958Z" fill="#003311"/>
<path d="M98.8739 6.70259H103.773V11.2842H98.8739V6.70259ZM98.8739 13.6681H103.773V33.0394H98.8739V13.6681Z" fill="#003311"/>
<path d="M118.81 13.2958C121.279 13.2958 123.519 14.1905 125.532 15.9784C127.544 17.7288 128.569 20.1878 128.569 23.3537C128.569 26.5197 127.544 28.9787 125.532 30.7666C123.519 32.5169 121.279 33.4117 118.81 33.4117C115.772 33.4117 113.532 31.8099 112.735 30.2817H112.468V39.9918H107.569V13.6681H112.468V16.4242H112.735C113.532 14.8975 115.772 13.2958 118.81 13.2958ZM114.063 27.6382C115.24 28.644 116.57 29.1648 118.126 29.1648C119.682 29.1648 121.012 28.644 122.113 27.6382C123.253 26.6324 123.822 25.1792 123.822 23.3537C123.822 21.5283 123.253 20.0751 122.113 19.0693C121.011 18.0635 119.682 17.5427 118.126 17.5427C116.57 17.5427 115.24 18.0635 114.063 19.0693C112.924 20.0751 112.353 21.5283 112.353 23.3537C112.353 25.1792 112.923 26.6324 114.063 27.6382Z" fill="#003311"/>
<path d="M139.835 6.58993C144.088 6.58993 147.089 9.01135 148.645 11.9912L144.962 14.1154C143.442 11.9177 142.037 10.9119 139.873 10.9119C137.633 10.9119 136.151 12.1038 136.151 13.8917C136.151 15.2698 137.253 16.3507 139.417 17.1328L141.24 17.8039C146.974 19.7779 149.366 22.0508 149.366 25.6641C149.366 28.0856 148.417 29.9845 146.518 31.3642C144.619 32.7423 142.417 33.4133 139.948 33.4133C137.631 33.4133 135.582 32.8174 133.796 31.6254C132.05 30.4335 130.871 28.9068 130.265 27.0063L134.101 25.0322C135.277 27.6774 137.253 28.9803 139.948 28.9803C142.493 28.9803 144.203 27.7884 144.203 25.5907C144.203 23.7652 143.101 23.0207 139.873 21.8287L138.202 21.1952C133.683 19.5934 131.214 17.5818 131.214 13.7448C131.214 11.5846 132.05 9.87019 133.683 8.56723C135.316 7.26263 137.366 6.59319 139.835 6.59319V6.58993Z" fill="#003311"/>
<path d="M162.409 13.6681V17.6161H158.195V27.1892C158.195 28.4186 158.841 29.0146 160.094 29.0146C160.74 29.0146 161.537 28.866 162.411 28.6048V32.5169C161.386 33.0019 160.171 33.2615 158.728 33.2615C155.272 33.2615 153.334 31.9944 153.334 28.2325V17.6161H150.561V13.6681H153.334V8.71418H158.195V13.6681H162.409Z" fill="#003311"/>
<path d="M173.495 33.4117C171.027 33.4117 168.748 32.5169 166.736 30.7666C164.723 28.9787 163.734 26.5197 163.734 23.3537C163.734 20.1878 164.722 17.7288 166.736 15.9784C168.748 14.1905 171.027 13.2958 173.495 13.2958C176.533 13.2958 178.774 14.8975 179.571 16.4242H179.837V13.6681H184.698V33.0394H179.837V30.2833H179.571C178.774 31.8099 176.533 33.4117 173.495 33.4117ZM170.153 27.6382C171.291 28.644 172.621 29.1648 174.178 29.1648C175.734 29.1648 177.064 28.644 178.203 27.6382C179.38 26.6324 179.949 25.1792 179.949 23.3537C179.949 21.5283 179.38 20.0751 178.203 19.0693C177.064 18.0635 175.734 17.5427 174.178 17.5427C172.621 17.5427 171.291 18.0635 170.153 19.0693C169.051 20.0751 168.482 21.5283 168.482 23.3537C168.482 25.1792 169.051 26.6324 170.153 27.6382Z" fill="#003311"/>
<path d="M199.229 13.6681V17.6161H195.015V27.1892C195.015 28.4186 195.66 29.0146 196.914 29.0146C197.56 29.0146 198.357 28.866 199.231 28.6048V32.5169C198.206 33.0019 196.99 33.2615 195.547 33.2615C192.092 33.2615 190.154 31.9944 190.154 28.2325V17.6161H187.381V13.6681H190.154V8.71418H195.015V13.6681H199.229Z" fill="#003311"/>
<path d="M201.911 6.70259H206.81V11.2842H201.911V6.70259ZM201.911 13.6681H206.81V33.0394H201.911V13.6681Z" fill="#003311"/>
<path d="M216.346 13.9669C218.738 13.0721 221.016 13.0721 223.41 13.9669C225.802 14.8241 227.968 16.3132 229.219 18.9958C230.549 21.6409 230.549 25.1416 229.219 27.7868C227.966 30.4319 225.802 31.921 223.41 32.7782C221.018 33.6354 218.739 33.6354 216.346 32.7782C213.952 31.921 211.826 30.4319 210.496 27.7868C209.243 25.1416 209.243 21.6409 210.496 18.9958C211.826 16.3132 213.952 14.8241 216.346 13.9669ZM214.295 23.3913C214.295 25.1792 214.864 26.5948 216.004 27.6382C217.143 28.644 218.435 29.1648 219.878 29.1648C221.359 29.1648 222.651 28.644 223.79 27.6382C224.928 26.5948 225.499 25.1792 225.499 23.3913C225.499 21.5658 224.93 20.1502 223.79 19.1069C222.651 18.0635 221.359 17.5427 219.878 17.5427C218.396 17.5427 217.106 18.0635 215.966 19.1069C214.864 20.1502 214.295 21.5658 214.295 23.3913Z" fill="#003311"/>
<path d="M244.388 13.2958C246.401 13.2958 248.11 14.0044 249.553 15.456C251.035 16.9091 251.756 19.0318 251.756 21.863V33.0378H246.857V22.1602C246.857 19.0318 245.034 17.5035 242.604 17.5035C241.274 17.5035 240.135 17.9884 239.261 18.9191C238.388 19.8498 237.895 20.7446 237.857 21.6018V33.0378H232.958V13.6664H237.857V17.4659C238.123 16.6087 238.844 15.7156 240.022 14.7473C241.199 13.7791 242.642 13.2958 244.388 13.2958Z" fill="#003311"/>
<path d="M257.921 12.7341C258.462 12.7341 258.94 12.9284 259.362 13.317C259.785 13.7056 260 14.1922 260 14.7751C260 15.358 259.785 15.8446 259.362 16.2332C258.94 16.6218 258.464 16.8161 257.921 16.8161C257.57 16.8161 257.237 16.7295 256.921 16.5646C256.604 16.3981 256.35 16.1532 256.14 15.838C255.937 15.518 255.837 15.1637 255.837 14.7751C255.837 14.3865 255.939 14.0322 256.14 13.717C256.551 13.077 257.225 12.7341 257.921 12.7341ZM257.921 16.3932C258.344 16.3932 258.725 16.2381 259.053 15.936C259.386 15.6274 259.552 15.2388 259.552 14.7751C259.552 14.3114 259.386 13.9228 259.053 13.6207C258.725 13.3121 258.344 13.157 257.921 13.157C257.498 13.157 257.112 13.3121 256.777 13.6207C256.45 13.9244 256.283 14.313 256.283 14.7751C256.283 15.2372 256.45 15.6274 256.777 15.936C257.11 16.2397 257.492 16.3932 257.921 16.3932ZM257.147 13.6778H257.884C258.402 13.6778 258.735 13.9636 258.735 14.3522C258.735 14.6902 258.562 14.9073 258.222 15.0151L258.853 15.8266H258.46L257.871 15.0314H257.49V15.8266H257.145V13.6762L257.147 13.6778ZM257.914 13.9685H257.485V14.7522H257.914C258.231 14.7522 258.385 14.62 258.385 14.3636C258.385 14.1073 258.207 13.9685 257.914 13.9685Z" fill="#003311"/>
</svg>
</file>

<file path="assets/js/checkout.js">
/**
 * ShipStation Checkout JS
 *
 * Handles persisting gift field values on the classic checkout using session storage.
 */
( function () {
	'use strict';

	// Get field IDs from localized script.
	const field_ids = wc_shipstation_checkout_params.field_ids || {};

	const is_gift_field_id      = field_ids.is_gift;
	const gift_message_field_id = field_ids.gift_message;

	const is_gift_field      = document.getElementById( is_gift_field_id );
	const gift_message_field = document.getElementById( gift_message_field_id );

	if ( ! is_gift_field || ! gift_message_field ) {
		return;
	}


	/**
	 * Save field value to session via AJAX.
	 *
	 * @param {string} field_id The field ID.
	 * @param {*} value The field value.
	 */
	function save_field_value_to_session( field_id, value ) {
		const data = new FormData();
		data.append( 'action', 'shipstation_save_field_value' );
		data.append( 'field_id', field_id );
		data.append( 'value', value );
		data.append( 'security', wc_checkout_params.update_order_review_nonce );

		fetch( wc_checkout_params.ajax_url, {
			method: 'POST',
			credentials: 'same-origin',
			body: data
		} ).catch( error => {
			console.error( 'Error saving field value to session:', error );
		} );
	}

	/**
	 * Load field values from session via AJAX.
	 */
	function load_field_values_from_session() {
		const data = new FormData();
		data.append( 'action', 'shipstation_load_field_values' );
		data.append( 'security', wc_checkout_params.update_order_review_nonce );

		fetch( wc_checkout_params.ajax_url, {
			method: 'POST',
			credentials: 'same-origin',
			body: data
		} )
			.then( response => response.json() )
			.then( data => {
				if ( data.success ) {
					// Set field values from session.
					if ( data.data.hasOwnProperty( is_gift_field_id ) ) {
						is_gift_field.checked = '1' === data.data[ is_gift_field_id ];
					}

					if ( data.data.hasOwnProperty( gift_message_field_id ) ) {
						gift_message_field.value = data.data[ gift_message_field_id ];
					}
				}
			} )
			.catch( error => {
				console.error( 'Error loading field values from session:', error );
			} );
	}

	// Add event listeners.
	is_gift_field.addEventListener( 'change', function () {
		save_field_value_to_session( is_gift_field_id, this.checked ? '1' : '0' );
	} );

	gift_message_field.addEventListener( 'change', function () {
		save_field_value_to_session( gift_message_field_id, this.value );
	} );

	let timeout;
	gift_message_field.addEventListener( 'input', function () {
		// Debounce for performance.
		clearTimeout( timeout );
		timeout = setTimeout( () => {
			save_field_value_to_session( gift_message_field_id, this.value );
		}, 500 );
	} );

	// Load field values from session when page loads.
	document.addEventListener( 'DOMContentLoaded', load_field_values_from_session );
} )();
</file>

<file path="assets/sass/admin.scss">
.shipstation-logo {
	width: 150px;
	margin: 16px 0 0 0;
}

.shipstation-external-link:after {
	font-family: "dashicons";
	content: "\f504";
	display: inline-block;
	vertical-align: bottom;
}

.woocommerce-shipstation-activation-notice-dismiss {
	position: relative;
	float: right;
	padding: 9px 0 9px 9px;
	text-decoration: none;
}
</file>

<file path="assets/sass/block-checkout.scss">
.wc-block-components-address-form__{
	&woocommerce_shipstation-shipstation_is_gift {
		& .wc-block-components-checkbox__label {

			&::after {
				content: var(--description-text);
				display: block;
				font-size: var(--wp--preset--font-size--small);
				color: var(--wc-secondary-text);
				white-space: normal !important;
			}
		}

		&:has(input:checked) + .wc-block-components-address-form__woocommerce_shipstation-shipstation_gift_message {
			display: block;
		}
	}
	&woocommerce_shipstation-shipstation_gift_message {
		display: none;

		&::after {
			content: var(--description-text);
			display: block;
			font-size: var(--wp--preset--font-size--small);
			color: var(--wc-secondary-text);
			margin-top: 0.5rem;
			white-space: normal !important;
		}
	}
}

.wp-block-woocommerce-order-confirmation-additional-fields-wrapper:has(> h3:only-child, > h2:only-child) {
	display: none;
}
</file>

<file path="assets/sass/classic-checkout.scss">
#customer_details .woocommerce-shipstation-gift {
	.description {
		all: unset;
		display: block !important;
		font-size: var(--wp--preset--font-size--small);
		height: auto !important;
	}
}

#shipstation_is_gift_field {
	&:has(input:checked) + #shipstation_gift_message_field {
		display: block;
	}
}

#shipstation_gift_message_field {
	--margin: 0.5rem 0 0 0;
	display: none;

	.description {
		margin: var(--margin) !important;
	}
}
</file>

<file path="includes/api/docs/inventory-api.md">
# ShipStation Inventory API Documentation

## Overview

The ShipStation Inventory API provides endpoints for retrieving and updating inventory stock data for WooCommerce products. This API allows integration with ShipStation to synchronize inventory levels between WooCommerce and ShipStation.

## Authentication

All API requests require authentication. The API uses WordPress REST API authentication methods, specifically requiring the `manage_woocommerce` capability.

## API Endpoints

### Get All Inventory

Retrieves inventory stock data for all products and variations with pagination.

**Endpoint:** `GET /wc-shipstation/v1/inventory`

**Parameters:**

| Parameter | Type    | Required | Default | Description                                      |
|-----------|---------|----------|---------|--------------------------------------------------|
| page      | integer | No       | 1       | Current page of the collection.                  |
| per_page  | integer | No       | 100     | Maximum number of items to be returned in result set. Maximum value is 500. |

**Response:**

```json
{
  "products": [
    {
      "product_id": 123,
      "sku": "PROD-123",
      "name": "Product Name",
      "stock_quantity": 10,
      "stock_status": "instock",
      "manage_stock": true,
      "backorders": "no"
    },
    {
      "product_id": 456,
      "sku": "PROD-456",
      "name": "Another Product",
      "stock_quantity": 5,
      "stock_status": "instock",
      "manage_stock": true,
      "backorders": "no"
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 100,
    "total_products": 250,
    "total_pages": 3,
    "has_more": true
  }
}
```

**Status Codes:**

- `200 OK`: Request successful.
- `500 Internal Server Error`: Error retrieving products.

### Get Inventory by Product ID

Retrieves inventory stock data for a specific product by ID.

**Endpoint:** `GET /wc-shipstation/v1/inventory/{product_id}`

**Parameters:**

| Parameter  | Type    | Required | Description                                      |
|------------|---------|----------|--------------------------------------------------|
| product_id | integer | Yes      | ID of the product to retrieve stock data for.    |

**Response:**

```json
{
  "product_id": 123,
  "sku": "PROD-123",
  "name": "Product Name",
  "stock_quantity": 10,
  "stock_status": "instock",
  "manage_stock": true,
  "backorders": "no"
}
```

**Status Codes:**

- `200 OK`: Request successful.
- `404 Not Found`: Product not found.

### Get Inventory by SKU

Retrieves inventory stock data for a specific product by SKU.

**Endpoint:** `GET /wc-shipstation/v1/inventory/sku/{sku}`

**Parameters:**

| Parameter | Type   | Required | Description                                      |
|-----------|--------|----------|--------------------------------------------------|
| sku       | string | Yes      | SKU of the product to retrieve stock data for.   |

**Response:**

```json
{
  "product_id": 123,
  "sku": "PROD-123",
  "name": "Product Name",
  "stock_quantity": 10,
  "stock_status": "instock",
  "manage_stock": true,
  "backorders": "no"
}
```

**Status Codes:**

- `200 OK`: Request successful.
- `404 Not Found`: Product not found.

### Update Inventory

Updates inventory stock levels for specified products by SKU or product ID.

**Endpoint:** `POST /wc-shipstation/v1/inventory/update`

**Request Body:**

```json
[
  {
    "product_id": 123,
    "stock_quantity": 15
  },
  {
    "sku": "PROD-456",
    "stock_quantity": 20
  }
]
```

You can update multiple products in a single request. Each item in the array must include either a `product_id` or `sku` to identify the product, and a `stock_quantity` value to set.

**Response:**

```json
{
  "message": "Inventory updated successfully.",
  "updated": [
    {
      "sku": "PROD-123",
      "product_id": 123,
      "stock": 15
    },
    {
      "sku": "PROD-456",
      "product_id": 456,
      "stock": 20
    }
  ],
  "updated_count": 2,
  "errors": [],
  "error_count": 0
}
```

**Possible Messages:**

- `"Inventory updated successfully."`: All items were updated successfully.
- `"Inventory updated with some errors."`: Some items were updated, but others had errors.
- `"No inventory updated due to errors."`: No items were updated due to errors.
- `"No inventory changes made."`: No changes were made (empty request or no valid items).

**Status Codes:**

- `200 OK`: Request processed (may include errors for individual items).
- `400 Bad Request`: Invalid request format.

## Error Handling

When errors occur with specific items in a batch update, the API will continue processing other items and return information about the errors:

```json
{
  "message": "Inventory updated with some errors.",
  "updated": [
    {
      "sku": "PROD-123",
      "product_id": 123,
      "stock": 15
    }
  ],
  "updated_count": 1,
  "errors": [
    {
      "item": {
        "sku": "INVALID-SKU",
        "stock_quantity": 10
      },
      "message": "Product not found"
    }
  ],
  "error_count": 1
}
```

## Examples

### Example: Get All Inventory (First Page)

**Request:**
```
GET /wc-shipstation/v1/inventory
```

### Example: Get All Inventory (Second Page with 50 Items Per Page)

**Request:**
```
GET /wc-shipstation/v1/inventory?page=2&per_page=50
```

### Example: Get Inventory by Product ID

**Request:**
```
GET /wc-shipstation/v1/inventory/123
```

### Example: Get Inventory by SKU

**Request:**
```
GET /wc-shipstation/v1/inventory/sku/PROD-123
```

### Example: Update Inventory for Multiple Products

**Request:**
```
POST /wc-shipstation/v1/inventory/update
```

**Request Body:**
```json
[
  {
    "product_id": 123,
    "stock_quantity": 15
  },
  {
    "sku": "PROD-456",
    "stock_quantity": 20
  }
]
```

## Notes

- When updating inventory, the API will automatically set `manage_stock` to `true` and update the `stock_status` based on the new stock quantity.
- For products with variations, you need to update each variation individually by its product ID or SKU.
- The maximum number of items per page is 500.
</file>

<file path="includes/api/docs/README.md">
# ShipStation API Documentation

This directory contains documentation for the ShipStation plugin's REST API endpoints.

## Available Documentation

- [Inventory API](inventory-api.md) - Documentation for the inventory management endpoints that allow retrieving and updating product stock levels.

## Purpose

This documentation is intended for developers who need to integrate with the ShipStation plugin's API. It provides detailed information about:

- Available endpoints
- Request parameters
- Response formats
- Authentication requirements
- Example requests and responses

## Authentication

All API endpoints require proper authentication. The API uses WordPress REST API authentication methods, specifically requiring the `manage_woocommerce` capability.

## Getting Started

To get started with the ShipStation API:

1. Review the documentation for the specific API you need to use
2. Ensure you have proper authentication credentials
3. Test your API requests using a tool like Postman or cURL
4. Implement the API calls in your application

## Need Help?

If you encounter any issues or have questions about the API, please refer to the [WooCommerce documentation](https://woocommerce.com/document/shipstation-for-woocommerce/) or contact support.
</file>

<file path="includes/api/requests/class-wc-safe-domdocument.php">
<?php
/**
 * Class WC_Safe_DOMDocument file.
 *
 * @package WC_ShipStation
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Drop in replacement for DOMDocument that is secure against XML eXternal Entity (XXE) Injection.
 * Bails if any DOCTYPE is found
 *
 * Comments in quotes come from the DOMDocument documentation: http://php.net/manual/en/class.domdocument.php
 */
class WC_Safe_DOMDocument extends DOMDocument {
	/**
	 * When called non-statically (as an object method) with malicious data, no Exception is thrown, but the object is emptied of all DOM nodes.
	 *
	 * @param string $filename The path to the XML document.
	 * @param int    $options  Bitwise OR of the libxml option constants. http://us3.php.net/manual/en/libxml.constants.php.
	 *
	 * @return bool|DOMDocument true on success, false on failure.  If called statically (E_STRICT error), returns DOMDocument on success.
	 *
	 * @throws Exception When the file is empty or not readable.
	 */
	public function load( $filename, $options = 0 ) {
		if ( '' === $filename ) {
			// "If an empty string is passed as the filename or an empty file is named, a warning will be generated."
			// "This warning is not generated by libxml and cannot be handled using libxml's error handling functions."
			throw new Exception( 'WC_Safe_DOMDocument::load(): Empty string supplied as input' );
		}

		if ( ! is_file( $filename ) || ! is_readable( $filename ) ) {
			// This warning probably would have been generated by libxml and could have been handled using libxml's error handling functions.
			// In WC_Safe_DOMDocument, however, we catch it before libxml, so it can't.
			// The alternative is to let file_get_contents() handle the error, but that's annoying.
			throw new Exception( 'WC_Safe_DOMDocument::load(): I/O warning : failed to load external entity "' . esc_html( sanitize_file_name( $filename ) ) . '"' );
		}

		if ( is_object( $this ) ) {
			// phpcs:ignore WordPress.WP.AlternativeFunctions.file_get_contents_file_get_contents --- Need to use this function to get the content on the file
			return $this->loadXML( file_get_contents( $filename ), $options );
		} else {
			// "This method *may* be called statically, but will issue an E_STRICT error."
			return self::loadXML( file_get_contents( $filename ), $options ); // phpcs:ignore WordPress.WP.AlternativeFunctions.file_get_contents_file_get_contents  --- Need to use this function to get the content on the file
		}
	}

	/**
	 * When called non-statically (as an object method) with malicious data, no Exception is thrown, but the object is emptied of all DOM nodes.
	 *
	 * @param string $source  The string containing the XML.
	 * @param int    $options Bitwise OR of the libxml option constants. http://us3.php.net/manual/en/libxml.constants.php.
	 *
	 * @return bool|DOMDocument true on success, false on failure.  If called statically (E_STRICT error), returns DOMDocument on success.
	 *
	 * @throws Exception When the file is empty or not readable.
	 *
	 * @todo Once PHP 8.0 is the minimum version, remove all libxml_disable_entity_loader() calls and the $old variable.
	 * @see  https://www.php.net/manual/en/function.libxml-disable-entity-loader.php#125661
	 */
	public function loadXML( $source, $options = 0 ) {
		if ( '' === $source ) {
			// "If an empty string is passed as the source, a warning will be generated."
			// "This warning is not generated by libxml and cannot be handled using libxml's error handling functions."
			throw new Exception( 'WC_Safe_DOMDocument::loadXML(): Empty string supplied as input' );
		}

		$old = null;

		if ( function_exists( 'libxml_disable_entity_loader' ) && \PHP_VERSION_ID < 80000 ) {
			$old = libxml_disable_entity_loader( true ); // phpcs:ignore Generic.PHP.DeprecatedFunctions.Deprecated --- This is the only way to disable the entity loader in versions of PHP prior to 8.0.
		}

		$return = parent::loadXML( $source, $options );

		if ( ! is_null( $old ) && \PHP_VERSION_ID < 80000 ) {
			libxml_disable_entity_loader( $old ); // phpcs:ignore Generic.PHP.DeprecatedFunctions.Deprecated --- This is the only way to disable the entity loader in versions of PHP prior to 8.0.
		}

		if ( ! $return ) {
			return $return;
		}

		// "This method *may* be called statically, but will issue an E_STRICT error."
		$is_this = is_object( $this );

		$object = $is_this ? $this : $return;

		if ( isset( $object->doctype ) ) {
			if ( $is_this ) {
				// Get rid of the dangerous input by removing *all* nodes.
				while ( $this->firstChild ) { // phpcs:ignore WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
					$this->removeChild( $this->firstChild ); // phpcs:ignore WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
				}
			}

			throw new Exception( 'WC_Safe_DOMDocument::loadXML(): Unsafe DOCTYPE Detected' );
		}

		return $return;
	}
}
</file>

<file path="includes/api/requests/class-wc-shipstation-api-request.php">
<?php
/**
 * WC_Shipstation_API_Request file.
 *
 * @package WC_ShipStation
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * WC_Shipstation_API_Request Class
 */
abstract class WC_Shipstation_API_Request {

	/**
	 * Stores logger class.
	 *
	 * @var WC_Logger
	 */
	private $log = null;

	/**
	 * Log something.
	 *
	 * @param string $message Log message.
	 */
	public function log( $message ) {
		if ( ! WC_ShipStation_Integration::$logging_enabled ) {
			return;
		}
		if ( is_null( $this->log ) ) {
			$this->log = new WC_Logger();
		}
		$this->log->add( 'shipstation', $message );
	}

	/**
	 * Run the request
	 */
	public function request() {}

	/**
	 * Validate data.
	 *
	 * @param array $required_fields fields to look for.
	 */
	protected function validate_input( $required_fields ) {
		foreach ( $required_fields as $required ) {
			// phpcs:ignore WordPress.Security.NonceVerification.Recommended --- Using WC_ShipStation_Integration::$auth_key for security verification
			if ( empty( $_GET[ $required ] ) ) {
				/* translators: 1: field name */
				$this->trigger_error( sprintf( esc_html__( 'Missing required param: %s', 'woocommerce-shipstation-integration' ), esc_html( $required ) ) );
			}
		}
	}

	/**
	 * Trigger and log an error.
	 *
	 * @param string $message Error message.
	 * @param int    $status_code Error status code.
	 */
	public function trigger_error( $message, $status_code = 400 ) { //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped, false positive on function parameter.
		$this->log( $message );
		wp_send_json_error( $message, $status_code );
	}
}
</file>

<file path="includes/api/requests/class-wc-shipstation-api-shipnotify.php">
<?php
/**
 * WC_Shipstation_API_Shipnotify file.
 *
 * @package WC_ShipStation
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * WC_Shipstation_API_Shipnotify Class
 */
class WC_Shipstation_API_Shipnotify extends WC_Shipstation_API_Request {

	/**
	 * Constructor.
	 */
	public function __construct() {
		if ( ! WC_Shipstation_API::authenticated() ) {
			exit;
		}
	}

	/**
	 * See how many items in the order need shipping.
	 *
	 * @param WC_Order $order Order object.
	 *
	 * @return int
	 */
	private function order_items_to_ship_count( $order ) {
		$needs_shipping = 0;

		foreach ( $order->get_items() as $item_id => $item ) {

			$product = is_callable( array( $item, 'get_product' ) ) ? $item->get_product() : false;

			if ( ( $product instanceof WC_Product ) && $product->needs_shipping() ) {
				$needs_shipping += ( $item['qty'] - abs( $order->get_qty_refunded_for_item( $item_id ) ) );
			}
		}

		return $needs_shipping;
	}

	/**
	 * Check whether a given item ID is shippable item.
	 *
	 * @since 4.1.16
	 * @version 4.1.16
	 *
	 * @param WC_Order $order   Order object.
	 * @param int      $item_id Item ID.
	 *
	 * @return bool Returns true if item is shippable product.
	 */
	private function is_shippable_item( $order, $item_id ) {
		$item    = $order->get_item( $item_id );
		$product = is_callable( array( $item, 'get_product' ) ) ? $item->get_product() : false;

		return $product ? $product->needs_shipping() : false;
	}

	/**
	 * Get the order ID from the order number.
	 *
	 * @param string $order_number Order number.
	 * @return integer
	 */
	private function get_order_id( $order_number ) {
		// Try to match an order number in brackets.
		preg_match( '/\((.*?)\)/', $order_number, $matches );
		if ( is_array( $matches ) && isset( $matches[1] ) ) {
			$order_id = $matches[1];

		} elseif ( function_exists( 'wc_sequential_order_numbers' ) ) {
			// Try to convert number for Sequential Order Number.
			$order_id = wc_sequential_order_numbers()->find_order_by_order_number( $order_number );

		} elseif ( function_exists( 'wc_seq_order_number_pro' ) ) {
			// Try to convert number for Sequential Order Number Pro.
			$order_id = wc_seq_order_number_pro()->find_order_by_order_number( $order_number );

		} elseif ( function_exists( 'run_wt_advanced_order_number' ) ) {
			// Try to convert order number for Sequential Order Number for WooCommerce by WebToffee.
			// This plugin does not have any function or method that we can use to convert the number.
			// So need to do it manually.
			$orders = wc_get_orders(
				array(
					'wt_order_number' => $order_number,
					'limit'           => 1,
					'return'          => 'ids',
				)
			);

			$order_id = ( is_array( $orders ) && ! empty( $orders ) ) ? array_shift( $orders ) : 0;
		} else {
			// Default to not converting order number.
			$order_id = $order_number;
		}

		if ( 0 === $order_id ) {
			$order_id = $order_number;
		}

		/**
		 * This order number can be adjusted by using a filter which is done by the
		 * Sequential Order Numbers / Sequential Order Numbers Pro plugins. However
		 * there are also many other plugins which offer this functionality.
		 *
		 * When the ShipNotify request is received the "real" order number is
		 * needed to be able to update the correct order. The plugin uses the
		 * function get_order_id. This function has specific compatibility for both
		 * Sequential Order Numbers & Sequential Order Numbers Pro. However there
		 * is no additional filter for plugins to modify this order ID if needed.
		 *
		 * @param int $order_id Order ID.
		 *
		 * @since 4.1.6
		 */
		return absint( apply_filters( 'woocommerce_shipstation_get_order_id', $order_id ) );
	}

	/**
	 * Get Parsed XML response.
	 *
	 * @param  string $xml XML.
	 * @return SimpleXMLElement|false
	 */
	private function get_parsed_xml( $xml ) {
		if ( ! class_exists( 'WC_Safe_DOMDocument' ) ) {
			include_once WC_SHIPSTATION_ABSPATH . 'includes/api/requests/class-wc-safe-domdocument.php';
		}

		libxml_use_internal_errors( true );

		$dom     = new WC_Safe_DOMDocument();
		$success = $dom->loadXML( $xml );

		if ( ! $success ) {
			$this->log( 'wpcom_safe_simplexml_load_string(): Error loading XML string' );
			return false;
		}

		if ( isset( $dom->doctype ) ) {
			$this->log( 'wpcom_safe_simplexml_import_dom(): Unsafe DOCTYPE Detected' );
			return false;
		}

		return simplexml_import_dom( $dom, 'SimpleXMLElement' );
	}

	/**
	 * Handling the request.
	 *
	 * @since 1.0.0
	 * @version 4.1.18
	 */
	public function request() {
		// phpcs:disable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase --- ShipStation provides an object with camelCase properties and method
		// phpcs:disable WordPress.Security.NonceVerification.Recommended --- Using WC_ShipStation_Integration::$auth_key for security verification
		$this->validate_input( array( 'order_number', 'carrier' ) );

		$timestamp          = wp_date( 'U' );
		$shipstation_xml    = file_get_contents( 'php://input' );
		$shipped_items      = array();
		$shipped_item_count = 0;
		$order_shipped      = false;
		$xml_order_id       = 0;

		$can_parse_xml = true;

		if ( empty( $shipstation_xml ) ) {
			$can_parse_xml = false;
			$this->log( __( 'Missing ShipNotify XML input.', 'woocommerce-shipstation-integration' ) );

			$mask = array(
				'auth_key'                         => '***',
				'woocommerce-login-nonce'          => '***',
				'_wpnonce'                         => '***',
				'woocommerce-reset-password-nonce' => '***',
			);

			$obfuscated_request = $mask + $_REQUEST;

			// For unknown reason raw post data can be empty. Log all requests
			// information might help figuring out the culprit.
			//
			// @see https://github.com/woocommerce/woocommerce-shipstation/issues/80.
			$this->log( '$_REQUEST: ' . print_r( $obfuscated_request, true ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r --- Its needed for logging
		}

		if ( ! function_exists( 'simplexml_import_dom' ) ) {
			$can_parse_xml = false;
			$this->log( __( 'Missing SimpleXML extension for parsing ShipStation XML.', 'woocommerce-shipstation-integration' ) );
		}

		$order_number = isset( $_GET['order_number'] ) ? wc_clean( wp_unslash( $_GET['order_number'] ) ) : '0';

		// Try to parse XML first since it can contain the real OrderID.
		if ( $can_parse_xml ) {
			$this->log( __( 'ShipNotify XML: ', 'woocommerce-shipstation-integration' ) . print_r( $shipstation_xml, true ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r --- Its needed for logging

			try {
				$xml = $this->get_parsed_xml( $shipstation_xml );
			} catch ( Exception $e ) {
				// translators: %s is an error message.
				$this->log( sprintf( __( 'Cannot parse XML : %s', 'woocommerce-shipstation-integration' ), $e->getMessage() ) );
				status_header( 500 );
			}

			if ( isset( $xml->ShipDate ) ) {
				$timestamp = strtotime( (string) $xml->ShipDate );
			}

			if ( isset( $xml->OrderID ) && $order_number !== (string) $xml->OrderID ) {
				$xml_order_id = (int) $xml->OrderID;
			}
		}

		// Get real order ID from XML otherwise try to convert it from the order number.
		$order_id        = ! $xml_order_id ? $this->get_order_id( $order_number ) : $xml_order_id;
		$tracking_number = empty( $_GET['tracking_number'] ) ? '' : wc_clean( wp_unslash( $_GET['tracking_number'] ) );
		$carrier         = empty( $_GET['carrier'] ) ? '' : wc_clean( wp_unslash( $_GET['carrier'] ) );
		$order           = wc_get_order( $order_id );

		if ( false === $order || ! is_object( $order ) ) {
			/* translators: %1$s is order number, %2$d is order id */
			$this->log( sprintf( __( 'Order number: %1$s or Order ID: %2$d can not be found.', 'woocommerce-shipstation-integration' ), $order_number, $order_id ) );
			exit;
		}

		// Get real order ID from order object.
		$order_id = $order->get_id();
		if ( empty( $order_id ) ) {
			/* translators: 1: order id */
			$this->log( sprintf( __( 'Invalid order ID: %s', 'woocommerce-shipstation-integration' ), $order_id ) );
			exit;
		}

		// Maybe parse items from posted XML (if exists).
		if ( $can_parse_xml && isset( $xml->Items ) ) {
			$items = $xml->Items;
			if ( $items ) {
				foreach ( $items->Item as $item ) {
					$this->log( __( 'ShipNotify Item: ', 'woocommerce-shipstation-integration' ) . print_r( $item, true ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r --- Its needed for logging

					$item_sku    = wc_clean( (string) $item->SKU );
					$item_name   = wc_clean( (string) $item->Name );
					$qty_shipped = absint( $item->Quantity );

					if ( $item_sku ) {
						$item_sku = ' (' . $item_sku . ')';
					}

					$item_id = wc_clean( (int) $item->LineItemID );
					if ( ! $this->is_shippable_item( $order, $item_id ) ) {
						/* translators: 1: item name */
						$this->log( sprintf( __( 'Item %s is not shippable product. Skipping.', 'woocommerce-shipstation-integration' ), $item_name ) );
						continue;
					}

					$shipped_item_count += $qty_shipped;
					$shipped_items[]     = $item_name . $item_sku . ' x ' . $qty_shipped;
				}
			}
		}

		// Number of items in WC order.
		$total_item_count = $this->order_items_to_ship_count( $order );

		// If we have a list of shipped items, we can customise the note + see
		// if the order is not yet complete.
		if ( count( $shipped_items ) > 0 ) {
			$order_note = sprintf(
				/* translators: 1) shipped items 2) carrier's name 3) shipped date, 4) tracking number */
				__( '%1$s shipped via %2$s on %3$s with tracking number %4$s.', 'woocommerce-shipstation-integration' ),
				esc_html( implode( ', ', $shipped_items ) ),
				esc_html( $carrier ),
				date_i18n( get_option( 'date_format' ), $timestamp ),
				$tracking_number
			);

			$current_shipped_items = max( (int) $order->get_meta( '_shipstation_shipped_item_count', true ), 0 );

			if ( ( $current_shipped_items + $shipped_item_count ) >= $total_item_count ) {
				$order_shipped = true;
			}

			$this->log(
				sprintf(
					/* translators: 1) number of shipped items 2) total shipped items 3) order ID */
					__( 'Shipped %1$d out of %2$d items in order %3$s', 'woocommerce-shipstation-integration' ),
					$shipped_item_count,
					$total_item_count,
					$order_id
				)
			);

			$order->update_meta_data( '_shipstation_shipped_item_count', $current_shipped_items + $shipped_item_count );
			$order->save_meta_data();
		} else {
			// If we don't have items from SS and order items in WC, or cannot parse
			// the XML, just complete the order as a whole.
			$order_shipped = 0 === $total_item_count || ! $can_parse_xml;

			$order_note = sprintf(
				/* translators: 1) carrier's name 2) shipped date, 3) tracking number */
				__( 'Items shipped via %1$s on %2$s with tracking number %3$s (Shipstation).', 'woocommerce-shipstation-integration' ),
				esc_html( $carrier ),
				date_i18n( get_option( 'date_format' ), $timestamp ),
				$tracking_number
			);

			/* translators: 1: order id */
			$this->log( sprintf( __( 'No items found - shipping entire order %d.', 'woocommerce-shipstation-integration' ), $order_id ) );
		}

		$current_status = 'wc-' . $order->get_status();

		// Tracking information - WC Shipment Tracking extension.
		if ( class_exists( 'WC_Shipment_Tracking' ) ) {
			if ( function_exists( 'wc_st_add_tracking_number' ) ) {
				wc_st_add_tracking_number( $order_id, $tracking_number, strtolower( $carrier ), $timestamp );
			} else {
				// You're using Shipment Tracking < 1.4.0. Please update!
				$order->update_meta_data( '_tracking_provider', strtolower( $carrier ) );
				$order->update_meta_data( '_tracking_number', $tracking_number );
				$order->update_meta_data( '_date_shipped', $timestamp );
				$order->save_meta_data();
			}

			$is_customer_note = false;
		} else {
			$is_customer_note = WC_ShipStation_Integration::$shipped_status !== $current_status;
		}

		$tracking_data = array(
			'tracking_number' => $tracking_number,
			'carrier'         => $carrier,
			'ship_date'       => $timestamp,
			'xml'             => $shipstation_xml,
		);

		/**
		* Allow to override tracking note.
		*
		* @param string $order_note
		* @param WC_Order $order
		* @param array $tracking_data
		*
		* @since 4.5.0
		*/
		$order_note = apply_filters(
			'woocommerce_shipstation_shipnotify_tracking_note',
			$order_note,
			$order,
			$tracking_data
		);

		$order->add_order_note(
			$order_note,
			/**
			* Allow to override should tracking note be sent to customer.
			*
			* @param bool $is_customer_note
			* @param string $order_note
			* @param WC_Order $order
			* @param array $tracking_data
			*
			* @since 4.5.0
			*/
			apply_filters(
				'woocommerce_shipstation_shipnotify_send_tracking_note',
				$is_customer_note,
				$order_note,
				$order,
				$tracking_data
			)
		);

		/**
		 * Trigger action for other integrations.
		 *
		 * @param WC_Order $order Order object.
		 * @param array    $tracking_data Tracking data.
		 *
		 * @since 4.0.1
		 */
		do_action(
			'woocommerce_shipstation_shipnotify',
			$order,
			$tracking_data
		);

		// Update order status.
		if (
			/**
			* Allow to override is order shipped flag.
			*
			* @param bool $order_shipped
			* @param WC_Order $order
			* @param array $tracking_data
			*
			* @since 4.5.0
			*/
			apply_filters(
				'woocommerce_shipstation_shipnotify_order_shipped',
				$order_shipped,
				$order,
				$tracking_data
			)
			&& WC_ShipStation_Integration::$shipped_status !== $current_status
		) {
			$order->update_status( WC_ShipStation_Integration::$shipped_status );

			/* translators: 1) order ID 2) shipment status */
			$this->log( sprintf( __( 'Updated order %1$s to status %2$s', 'woocommerce-shipstation-integration' ), $order_id, WC_ShipStation_Integration::$shipped_status ) );

			/**
			 * Trigger action after the order status is changed for other integrations.
			 *
			 * @param WC_Order $order Order object.
			 * @param array    $tracking_data Tracking data.
			 *
			 * @since 4.5.2
			 */
			do_action(
				'woocommerce_shipstation_shipnotify_status_updated',
				$order,
				$tracking_data
			);
		}

		status_header( 200 );
		// phpcs:enable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
		// phpcs:enable WordPress.Security.NonceVerification.Recommended
	}
}

return new WC_Shipstation_API_Shipnotify();
</file>

<file path="includes/api/rest/class-inventory-controller.php">
<?php
/**
 * ShipStation REST API Inventory Controller file.
 *
 * @package WC_ShipStation
 */

namespace WooCommerce\Shipping\ShipStation\API\REST;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use WC_Product;
use WP_REST_Request;
use WP_REST_Response;
use Automattic\WooCommerce\Enums\ProductType;
use WP_REST_Server;

/**
 * Inventory_Controller class.
 */
class Inventory_Controller {

	/**
	 * Namespace for the REST API
	 *
	 * @var string
	 */
	protected string $namespace = 'wc-shipstation/v1';

	/**
	 * REST base for the controller.
	 *
	 * @var string
	 */
	protected string $rest_base = 'inventory';

	/**
	 * Register the routes for the controller.
	 */
	public function register_routes(): void {

		// Register the endpoint for retrieving stock data.
		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base,
			array(
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => array( $this, 'get_inventory' ),
				'permission_callback' => array( $this, 'check_get_permission' ),
				'args'                => array(
					'page'     => array(
						'description'       => __( 'Current page of the collection.', 'woocommerce-shipstation-integration' ),
						'type'              => 'integer',
						'default'           => 1,
						'sanitize_callback' => function ( $value ) {
							return max( 1, absint( $value ) );
						},
						'validate_callback' => function ( $param ) {
							return is_numeric( $param );
						},
					),
					'per_page' => array(
						'description'       => __( 'Maximum number of items to be returned in result set.', 'woocommerce-shipstation-integration' ),
						'type'              => 'integer',
						'default'           => 100,
						'sanitize_callback' => function ( $value ) {
							return min( max( - 1, absint( $value ) ), 500 ); // Limit between 1 and 500.
						},
						'validate_callback' => function ( $param ) {
							return is_numeric( $param );
						},
					),
				),
			)
		);

		// Register the endpoint for retrieving stock data by product ID.
		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/(?P<product_id>\d+)',
			array(
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => array( $this, 'get_inventory_by_id' ),
				'permission_callback' => array( $this, 'check_get_permission' ),
				'args'                => array(
					'product_id' => array(
						'description'       => __( 'ID of the product to retrieve stock data for.', 'woocommerce-shipstation-integration' ),
						'type'              => 'integer',
						'required'          => true,
						'sanitize_callback' => 'absint',
						'validate_callback' => function ( $param ) {
							return is_numeric( $param );
						},
					),
				),
			)
		);

		// Register the endpoint for updating stock data by SKU.
		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/sku/(?P<sku>[\w-]+)',
			array(
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => array( $this, 'get_inventory_by_sku' ),
				'permission_callback' => array( $this, 'check_get_permission' ),
				'args'                => array(
					'sku' => array(
						'description'       => __( 'SKU of the product to retrieve stock data for.', 'woocommerce-shipstation-integration' ),
						'type'              => 'string',
						'required'          => true,
						'sanitize_callback' => 'sanitize_text_field',
						'validate_callback' => function ( $param ) {
							return is_string( $param );
						},
					),
				),
			)
		);

		// Register the endpoint for updating stock data.
		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/update',
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array( $this, 'update_inventory' ),
				'permission_callback' => array( $this, 'check_update_permission' ),
			)
		);
	}

	/**
	 * REST API permission callback.
	 *
	 * @return boolean
	 */
	public function check_get_permission(): bool {
		/**
		 * Filters whether the current user has permissions to manage WooCommerce.
		 *
		 * @since 1.0.0
		 *
		 * @param bool $can_manage_wc Whether the user can manage WooCommerce.
		 */
		return apply_filters( 'wc_shipstation_user_can_manage_wc', wc_rest_check_manager_permissions( 'attributes', 'read' ) );
	}

	/**
	 * REST API permission callback.
	 *
	 * @return boolean
	 */
	public function check_update_permission(): bool {
		/**
		 * Filters whether the current user has permissions to manage WooCommerce.
		 *
		 * @since 1.0.0
		 *
		 * @param bool $can_manage_wc Whether the user can manage WooCommerce.
		 */
		return apply_filters( 'wc_shipstation_user_can_manage_wc', wc_rest_check_manager_permissions( 'attributes', 'create' ) );
	}

	/**
	 * Get product data for API response.
	 *
	 * @param WC_Product $product Product object.
	 *
	 * @return array Product data.
	 */
	private function get_product_data( WC_Product $product ): array {
		$product_data = array(
			'product_id'     => $product->get_id(),
			'sku'            => $product->get_sku(),
			'name'           => $product->get_name(),
			'stock_quantity' => $product->get_stock_quantity(),
			'stock_status'   => $product->get_stock_status(),
			'manage_stock'   => $product->get_manage_stock(),
			'backorders'     => $product->get_backorders(),
		);

		// Add the parent_id when relevant.
		$parent_id = $product->get_parent_id();
		if ( $parent_id ) {
			$product_data['parent_id'] = $parent_id;
		}

		return $product_data;
	}

	/**
	 * Get product data by product ID.
	 *
	 * @param int $product_id Product ID.
	 *
	 * @return array
	 */
	public function get_product_data_by_id( int $product_id ): array {
		if ( $product_id <= 0 ) {
			return array();
		}

		$product = wc_get_product( $product_id );

		if ( ! $product ) {
			return array();
		}

		return $this->get_product_data( $product );
	}

	/**
	 * Get a REST response with the provided product data.
	 *
	 * @param array $product_data Product data array.
	 *
	 * @return WP_REST_Response
	 */
	private function get_product_response( array $product_data ): WP_REST_Response {
		if ( empty( $product_data ) ) {
			return new WP_REST_Response( array( 'message' => __( 'Product not found.', 'woocommerce-shipstation-integration' ) ), 404 );
		}

		return new WP_REST_Response( $product_data, 200 );
	}

	/**
	 * Retrieve the inventory stock data for a specific product by ID.
	 *
	 * @param WP_REST_Request $request Request object.
	 *
	 * @return WP_REST_Response
	 */
	public function get_inventory_by_id( WP_REST_Request $request ): WP_REST_Response {
		// Get the product ID from the request.
		$product_id   = (int) $request->get_param( 'product_id' );
		$product_data = $this->get_product_data_by_id( $product_id );

		return $this->get_product_response( $product_data );
	}

	/**
	 * Retrieve the inventory stock data for a specific product by SKU.
	 *
	 * @param WP_REST_Request $request Request object.
	 *
	 * @return WP_REST_Response
	 */
	public function get_inventory_by_sku( WP_REST_Request $request ): WP_REST_Response {
		// Get the SKU from the request.
		$sku          = (string) $request->get_param( 'sku' );
		$product_id   = wc_get_product_id_by_sku( wc_clean( wp_unslash( $sku ) ) );
		$product_data = $this->get_product_data_by_id( $product_id );

		return $this->get_product_response( $product_data );
	}

	/**
	 * Retrieve the inventory stock data for all products and variations.
	 *
	 * @param WP_REST_Request $request Request object.
	 *
	 * @return WP_REST_Response
	 */
	public function get_inventory( WP_REST_Request $request ): WP_REST_Response {
		$request_params = $request->get_params();

		// Get pagination parameters.
		$page     = absint( $request_params['page'] ); // Default to page 1.
		$per_page = intval( $request_params['per_page'] ); // Default to 100 items per page.

		$args = array(
			'type'     => array( ProductType::SIMPLE, ProductType::VARIABLE, ProductType::GROUPED, ProductType::EXTERNAL, ProductType::VARIATION ),
			'limit'    => $per_page,
			'page'     => $page,
			'paginate' => true,
		);

		$results = wc_get_products( $args );

		if ( is_wp_error( $results ) ) {
			return new WP_REST_Response( array( 'message' => __( 'Error retrieving products.', 'woocommerce-shipstation-integration' ) ), 500 );
		}

		$total_products = $results->total;

		// Calculate pagination information.
		$total_pages = $results->max_num_pages;
		$has_more    = $page < $total_pages;

		// Prepare the response data.
		$inventory_data = array(
			'products'   => array(),
			'pagination' => array(
				'page'           => $page,
				'per_page'       => $per_page,
				'total_products' => $total_products,
				'total_pages'    => $total_pages,
				'has_more'       => $has_more,
			),
		);

		if ( empty( $results->products ) || empty( $results->total ) ) {
			// No products found, return an empty response.
			return new WP_REST_Response( $inventory_data, 200 );
		}

		foreach ( $results->products as $product ) {
			$inventory_data['products'][] = $this->get_product_data( $product );
		}

		return new WP_REST_Response( $inventory_data, 200 );
	}

	/**
	 * Update inventory stock for specified SKUs (both products and variations).
	 *
	 * @param WP_REST_Request $request Request object.
	 *
	 * @return WP_REST_Response
	 */
	public function update_inventory( WP_REST_Request $request ): WP_REST_Response {
		$items = $request->get_json_params();

		if ( empty( $items ) || ! is_array( $items ) ) {
			return new WP_REST_Response( 'Invalid request format.', 400 );
		}

		$updated = array();
		$errors  = array();

		foreach ( $items as $item ) {
			if ( ( empty( $item['product_id'] ) && empty( $item['sku'] ) ) || ! isset( $item['stock_quantity'] ) ) {
				$errors[] = array(
					'item'    => $item,
					'message' => __( 'Invalid item', 'woocommerce-shipstation-integration' ),
				);
				continue; // Skip invalid items.
			}

			if ( ! empty( $item['product_id'] ) && ! is_numeric( $item['product_id'] ) ) {
				$errors[] = array(
					'item'    => $item,
					'message' => __( 'Product ID is not numeric', 'woocommerce-shipstation-integration' ),
				);
				continue; // Skip if product_id is not numeric.
			}

			if ( ! isset( $item['stock_quantity'] ) || ! is_numeric( $item['stock_quantity'] ) ) {
				$errors[] = array(
					'item'    => $item,
					'message' => __( 'Stock quantity is not set or not numeric', 'woocommerce-shipstation-integration' ),
				);
				continue; // Skip if stock_quantity is not set or not numeric.
			}

			if ( ! empty( $item['product_id'] ) ) {
				$product = wc_get_product( absint( $item['product_id'] ) );
			} else {
				$product = wc_get_product( wc_get_product_id_by_sku( wc_clean( wp_unslash( $item['sku'] ) ) ) );
			}

			if ( ! $product ) {
				$errors[] = array(
					'item'    => $item,
					'message' => __( 'Product not found', 'woocommerce-shipstation-integration' ),
				);
				continue; // Skip if product does not exist.
			}

			$stock_qty = (int) $item['stock_quantity'];

			$product->set_manage_stock( true );
			$product->set_stock_quantity( $stock_qty );
			$product->set_stock_status( $stock_qty > 0 ? 'instock' : 'outofstock' );
			$product->save();

			$updated[] = array(
				'sku'        => $product->get_sku(),
				'product_id' => $product->get_id(),
				'stock'      => $stock_qty,
			);
		}

		$message = __( 'Inventory updated successfully.', 'woocommerce-shipstation-integration' );

		if ( count( $errors ) > 0 && count( $updated ) === 0 ) {
			// If there are errors and no successful updates, return the errors.
			$message = __( 'No inventory updated due to errors.', 'woocommerce-shipstation-integration' );
		}

		if ( count( $errors ) > 0 && count( $updated ) > 0 ) {
			// If there are errors but some updates were successful, return both.
			$message = __( 'Inventory updated with some errors.', 'woocommerce-shipstation-integration' );
		}

		if ( count( $errors ) === 0 && count( $updated ) === 0 ) {
			// If there are no errors and no updates, return a message indicating no changes.
			$message = __( 'No inventory changes made.', 'woocommerce-shipstation-integration' );
		}

		return new WP_REST_Response(
			array(
				'message'       => $message,
				'updated'       => $updated,
				'updated_count' => count( $updated ),
				'errors'        => $errors,
				'error_count'   => count( $errors ),
			),
			200
		);
	}
}
</file>

<file path="includes/class-rest-api-loader.php">
<?php
/**
 * ShipStation REST API Loader file.
 *
 * @package WC_ShipStation
 */

namespace WooCommerce\Shipping\ShipStation;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use WooCommerce\Shipping\ShipStation\API\REST\Inventory_Controller;

/**
 * Class REST_API_Loader
 *
 * This class is responsible for loading the REST API routes for the ShipStation integration.
 */
class REST_API_Loader {
	/**
	 * Initialize the REST API routes.
	 */
	public function init() {
		// Include Inventory REST API class file.
		require_once WC_SHIPSTATION_ABSPATH . 'includes/api/rest/class-inventory-controller.php';

		// Register the REST API routes.
		add_action( 'rest_api_init', array( $this, 'register_routes' ) );
		add_filter( 'woocommerce_rest_api_get_rest_namespaces', array( $this, 'register_shipstation_namespaces' ) );
	}

	/**
	 * Register the REST API routes.
	 */
	public function register_routes() {
		$inventory_controller = new Inventory_Controller();
		$inventory_controller->register_routes();
	}

	/**
	 * Registers the ShipStation namespaces for the REST API.
	 *
	 * @param array $controllers List of current REST API controllers.
	 *
	 * @return array Updated list of REST API controllers with added ShipStation namespaces.
	 */
	public function register_shipstation_namespaces( array $controllers ): array {
		$controllers['wc-shipstation/v1']['inventory'] = 'WooCommerce\Shipping\ShipStation\API\REST\Inventory_Controller';

		return $controllers;
	}
}
</file>

<file path="includes/trait-woocommerce-order-util.php">
<?php
/**
 * Class WC_ShipStation\Order_Util file.
 *
 * @package WC_ShipStation
 */

namespace WooCommerce\ShipStation;

use Automattic\WooCommerce\Utilities\OrderUtil;
use WC_Order;
use WP_Post;

defined( 'ABSPATH' ) || exit;

/**
 * Trait Order_Util
 *
 * A proxy-style trait that will help keep our code more stable and cleaner during the
 * transition to WC Custom Order Tables.
 */
trait Order_Util {
	/**
	 * Constant variable for admin screen name.
	 *
	 * @var string $legacy_order_admin_screen.
	 */
	public static string $legacy_order_admin_screen = 'shop_order';

	/**
	 * Checks whether the OrderUtil class exists
	 *
	 * @return bool
	 */
	public static function wc_order_util_class_exists(): bool {
		return class_exists( 'Automattic\WooCommerce\Utilities\OrderUtil' );
	}

	/**
	 * Checks whether the OrderUtil class and the given method exist
	 *
	 * @param String $method_name Class method name.
	 *
	 * @return bool
	 */
	public static function wc_order_util_method_exists( string $method_name ): bool {
		if ( ! self::wc_order_util_class_exists() ) {
			return false;
		}

		if ( ! method_exists( 'Automattic\WooCommerce\Utilities\OrderUtil', $method_name ) ) {
			return false;
		}

		return true;
	}

	/**
	 * Checks whether we are using custom order tables.
	 *
	 * @return bool
	 */
	public static function custom_orders_table_usage_is_enabled(): bool {
		if ( ! self::wc_order_util_method_exists( 'custom_orders_table_usage_is_enabled' ) ) {
			return false;
		}

		return OrderUtil::custom_orders_table_usage_is_enabled();
	}

	/**
	 * Returns the relevant order screen depending on whether
	 * custom order tables are being used.
	 *
	 * @return string
	 */
	public static function get_order_admin_screen(): string {
		if ( ! self::wc_order_util_method_exists( 'get_order_admin_screen' ) ) {
			return self::$legacy_order_admin_screen;
		}

		return OrderUtil::get_order_admin_screen();
	}

	/**
	 * Check if the object is WP_Post object.
	 *
	 * @param Mixed $post_object Either Post object or Order object.
	 *
	 * @return Boolean
	 */
	public static function is_wp_post( $post_object ): bool {
		return ( $post_object instanceof WP_Post );
	}

	/**
	 * Check if the object is WC_Order object.
	 *
	 * @param Mixed $post_object Either Post object or Order object.
	 *
	 * @return Boolean
	 */
	public static function is_wc_order( $post_object ): bool {
		return ( $post_object instanceof WC_Order );
	}

	/**
	 * Check if the object is either WP_Post or WC_Order object.
	 *
	 * @param Mixed $post_object Either Post object or Order object.
	 *
	 * @return Boolean
	 */
	public static function is_order_or_post( $post_object ): bool {
		return self::is_wp_post( $post_object ) || self::is_wc_order( $post_object );
	}

	/**
	 * Returns the WC_Order object from the object passed to
	 * the add_meta_box callback function.
	 *
	 * @param WC_Order|WP_Post $post_or_order_object Either Post object or Order object.
	 *
	 * @return WC_Order
	 */
	public static function init_theorder_object( $post_or_order_object ): WC_Order {
		if ( ! self::wc_order_util_method_exists( 'init_theorder_object' ) ) {
			return wc_get_order( $post_or_order_object->ID );
		}

		return OrderUtil::init_theorder_object( $post_or_order_object );
	}
}
</file>

<file path=".nvmrc">
22.14.0
</file>

<file path=".phpcs.security.xml">
<?xml version="1.0"?>
<ruleset name="Security sniffs from WordPress Coding Standards">
  <description>Security sniffs from WordPress Coding Standards</description>

  <arg value="sp"/>
  <arg name="colors"/>
  <arg name="extensions" value="php"/>
  <arg name="parallel" value="8"/>

  <config name="testVersion" value="7.2-"/>

  <!-- Do not fail PHPCS CI over warnings -->
  <config name="ignore_warnings_on_exit" value="1"/>

  <rule ref="WordPress.Security.EscapeOutput"/>
  <rule ref="WordPress.Security.ValidatedSanitizedInput.InputNotSanitized"/>
  <rule ref="WordPress.Security.EscapeOutput">
    <properties>
      <property name="customEscapingFunctions" type="array" value="wc_help_tip,wc_sanitize_tooltip,wc_selected,wc_kses_notice,wc_esc_json,wc_query_string_form_fields,wc_make_phone_clickable" />
    </properties>
  </rule>
  <rule ref="WordPress.Security.ValidatedSanitizedInput">
    <properties>
      <property name="customSanitizingFunctions" type="array" value="wc_clean,wc_sanitize_tooltip,wc_format_decimal,wc_stock_amount,wc_sanitize_permalink,wc_sanitize_textarea" />
    </properties>
  </rule>
  <!-- Encourage use of wp_safe_redirect() to avoid open redirect vulnerabilities.
     https://github.com/WordPress/WordPress-Coding-Standards/pull/1264 -->
  <rule ref="WordPress.Security.SafeRedirect"/>

  <!-- Verify that a nonce check is done before using values in superglobals.
     https://github.com/WordPress/WordPress-Coding-Standards/issues/73 -->
  <rule ref="WordPress.Security.NonceVerification"/>

  <!-- https://github.com/WordPress/WordPress-Coding-Standards/issues/1157 -->
  <rule ref="WordPress.Security.PluginMenuSlug"/>

  <!-- Covers rule: The eval() construct is very dangerous, and is impossible to secure. ... these must not be used. -->
  <rule ref="Squiz.PHP.Eval.Discouraged">
    <type>error</type>
    <message>eval() is a security risk so not allowed.</message>
  </rule>

</ruleset>
</file>

<file path="includes/data/data-settings.php">
<?php
/**
 * Data for the settings page file.
 *
 * @package WC_ShipStation
 */

$statuses = wc_get_order_statuses();

// When integration loaded custom statuses is not loaded yet, so we need to
// merge it manually.
if ( function_exists( 'wc_order_status_manager' ) ) {
	$query = new WP_Query(
		array(
			'post_type'        => 'wc_order_status',
			'post_status'      => 'publish',
			'posts_per_page'   => -1,
			'suppress_filters' => 1,
			'orderby'          => 'menu_order',
			'order'            => 'ASC',
		)
	);

	$filtered_statuses = array();
	foreach ( $query->posts as $post_status ) {
		$filtered_statuses[ 'wc-' . $post_status->post_name ] = $post_status->post_title;
	}
	$statuses = array_merge( $statuses, $filtered_statuses );

	wp_reset_postdata();
}

foreach ( $statuses as $key => $value ) {
	$statuses[ $key ] = str_replace( 'wc-', '', $key );
}

$fields = array(
	'auth_key'        => array(
		'title'             => __( 'Authentication Key', 'woocommerce-shipstation-integration' ),
		'description'       => __( 'Copy and paste this key into ShipStation during setup.', 'woocommerce-shipstation-integration' ),
		'default'           => '',
		'type'              => 'text',
		'desc_tip'          => __( 'This is the <code>Auth Key</code> you set in ShipStation and allows ShipStation to communicate with your store.', 'woocommerce-shipstation-integration' ),
		'custom_attributes' => array(
			'readonly' => 'readonly',
		),
		'value'             => WC_ShipStation_Integration::$auth_key,
	),
	'export_statuses' => array(
		'title'             => __( 'Export Order Statuses&hellip;', 'woocommerce-shipstation-integration' ),
		'type'              => 'multiselect',
		'options'           => $statuses,
		'class'             => 'chosen_select',
		'css'               => 'width: 450px;',
		'description'       => __( 'Define the order statuses you wish to export to ShipStation.', 'woocommerce-shipstation-integration' ),
		'desc_tip'          => true,
		'custom_attributes' => array(
			'data-placeholder' => __( 'Select Order Statuses', 'woocommerce-shipstation-integration' ),
		),
	),
	'shipped_status'  => array(
		'title'       => __( 'Shipped Order Status&hellip;', 'woocommerce-shipstation-integration' ),
		'type'        => 'select',
		'options'     => $statuses,
		'description' => __( 'Define the order status you wish to update to once an order has been shipping via ShipStation. By default this is "Completed".', 'woocommerce-shipstation-integration' ),
		'desc_tip'    => true,
		'default'     => 'wc-completed',
	),
	'gift_enabled'    => array(
		'title'       => __( 'Gift', 'woocommerce-shipstation-integration' ),
		'label'       => __( 'Enable Gift options at checkout page', 'woocommerce-shipstation-integration' ),
		'type'        => 'checkbox',
		'description' => __( 'Allow customer to mark their order as a gift and include a personalized message.', 'woocommerce-shipstation-integration' ),
		'desc_tip'    => __( 'Enable gift fields on the checkout page.', 'woocommerce-shipstation-integration' ),
		'default'     => 'no',
	),
	'logging_enabled' => array(
		'title'       => __( 'Logging', 'woocommerce-shipstation-integration' ),
		'label'       => __( 'Enable Logging', 'woocommerce-shipstation-integration' ),
		'type'        => 'checkbox',
		'description' => __( 'Note: this may log personal information. We recommend using this for debugging purposes only and deleting the logs when finished.', 'woocommerce-shipstation-integration' ),
		'desc_tip'    => __( 'Log all API interactions.', 'woocommerce-shipstation-integration' ),
		'default'     => 'yes',
	),
);

return $fields;
</file>

<file path=".gitignore">
/nbproject/private/
node_modules
project.xml
project.properties
.DS_Store
Thumbs.db
.buildpath
.project
.settings*
.vscode
sftp-config.json
/deploy/
/wc-apidocs/
/languages/
screenshots/
/assets/**/*.min.js
/assets/**/*.css

# Ignore all log files except for .htaccess
/logs/*
!/logs/.htaccess

tests/e2e/config/local-*
.eslintcache

/vendor/

./dist
.idea

woocommerce-shipstation.zip
</file>

<file path="includes/api/requests/class-wc-shipstation-api-export.php">
<?php
/**
 * WC_Shipstation_API_Export file.
 *
 * @package WC_ShipStation
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use WooCommerce\Shipping\ShipStation\Checkout;
use WooCommerce\ShipStation\Order_Util;

/**
 * WC_Shipstation_API_Export Class
 */
class WC_Shipstation_API_Export extends WC_Shipstation_API_Request {
	use Order_Util;

	/**
	 * Constructor
	 */
	public function __construct() {
		if ( ! WC_Shipstation_API::authenticated() ) {
			exit;
		}
	}

	/**
	 * Preparing `IN` sql statement using `WPDB::prepare()`.
	 *
	 * @param array $values IN values.
	 */
	private static function prepare_in( $values ) {
		return implode(
			',',
			array_map(
				function ( $value ) {
					global $wpdb;

					// Use the official prepare() function to sanitize the value.
					return $wpdb->prepare( '%s', $value );
				},
				$values
			)
		);
	}

	/**
	 * Do the request
	 */
	public function request() {
		global $wpdb;
		// phpcs:disable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase --- ShipStation provides an object with camelCase properties and method
		// phpcs:disable WordPress.Security.NonceVerification.Recommended --- Using WC_ShipStation_Integration::$auth_key for security verification
		$this->validate_input( array( 'start_date', 'end_date' ) );

		header( 'Content-Type: text/xml' );
		$xml               = new DOMDocument( '1.0', 'utf-8' );
		$xml->formatOutput = true;
		$page              = max( 1, isset( $_GET['page'] ) ? absint( $_GET['page'] ) : 1 );
		$exported          = 0;
		$tz_offset         = get_option( 'gmt_offset' ) * 3600;
		$raw_start_date    = isset( $_GET['start_date'] ) ? urldecode( wc_clean( wp_unslash( $_GET['start_date'] ) ) ) : false;
		$raw_end_date      = isset( $_GET['end_date'] ) ? urldecode( wc_clean( wp_unslash( $_GET['end_date'] ) ) ) : false;
		$store_weight_unit = get_option( 'woocommerce_weight_unit' );
		// phpcs:enable WordPress.Security.NonceVerification.Recommended
		// Parse start and end date.
		if ( $raw_start_date && false === strtotime( $raw_start_date ) ) {
			$month      = substr( $raw_start_date, 0, 2 );
			$day        = substr( $raw_start_date, 2, 2 );
			$year       = substr( $raw_start_date, 4, 4 );
			$time       = substr( $raw_start_date, 9, 4 );
			$start_date = gmdate( 'Y-m-d H:i:s', strtotime( $year . '-' . $month . '-' . $day . ' ' . $time ) );
		} else {
			$start_date = gmdate( 'Y-m-d H:i:s', strtotime( $raw_start_date ) );
		}

		if ( $raw_end_date && false === strtotime( $raw_end_date ) ) {
			$month    = substr( $raw_end_date, 0, 2 );
			$day      = substr( $raw_end_date, 2, 2 );
			$year     = substr( $raw_end_date, 4, 4 );
			$time     = substr( $raw_end_date, 9, 4 );
			$end_date = gmdate( 'Y-m-d H:i:s', strtotime( $year . '-' . $month . '-' . $day . ' ' . $time ) );
		} else {
			$end_date = gmdate( 'Y-m-d H:i:s', strtotime( $raw_end_date ) );
		}

		$orders_to_export = wc_get_orders(
			array(
				'date_modified' => strtotime( $start_date ) . '...' . strtotime( $end_date ),
				'type'          => 'shop_order',
				'status'        => WC_ShipStation_Integration::$export_statuses,
				'return'        => 'ids',
				'orderby'       => 'date_modified',
				'order'         => 'DESC',
				'paged'         => $page,
				'limit'         => WC_SHIPSTATION_EXPORT_LIMIT,
			)
		);

		$total_orders_to_export = wc_get_orders(
			array(
				'type'          => 'shop_order',
				'date_modified' => strtotime( $start_date ) . '...' . strtotime( $end_date ),
				'status'        => WC_ShipStation_Integration::$export_statuses,
				'paginate'      => true,
				'return'        => 'ids',
			)
		);

		$max_results = $total_orders_to_export->total;

		$orders_xml = $xml->createElement( 'Orders' );

		/**
		 * Loop through each order ID and process for export.
		 *
		 * @var int $order_id
		 */
		foreach ( $orders_to_export as $order_id ) {
			/**
			 * Allow third party to skip the export of certain order ID.
			 *
			 * @param boolean $flag Flag to skip the export.
			 * @param int     $order_id Order ID.
			 *
			 * @since 4.1.42
			 */
			if ( ! apply_filters( 'woocommerce_shipstation_export_order', true, $order_id ) ) {
				continue;
			}

			/**
			 * Allow third party to change the order object.
			 *
			 * @param WC_Order $order Order object.
			 *
			 * @since 4.1.42
			 */
			$order = apply_filters( 'woocommerce_shipstation_export_get_order', wc_get_order( $order_id ) );

			if ( ! self::is_wc_order( $order ) ) {
				/* translators: 1: order id */
				$this->log( sprintf( __( 'Order %s can not be found.', 'woocommerce-shipstation-integration' ), $order_id ) );
				continue;
			}

			/**
			 * Currency code and exchange rate filters.
			 *
			 * These two filters allow 3rd parties to modify the currency code
			 * and exchange rate used for the order before exporting to ShipStation.
			 *
			 * This can be necessary in cases where the order currency doesn't match
			 * the ShipStation account currency. ShipStation does not do currency
			 * conversion, so the conversion must be done before the order is exported.
			 *
			 * @param string   $currency_code The currency code to use for the order.
			 * @param WC_Order $order WooCommerce Order object.
			 *
			 * @since 4.3.7
			 */
			$currency_code = apply_filters( 'woocommerce_shipstation_export_currency_code', $order->get_currency(), $order );
			/**
			 * Allow 3rd parties to modify the exchange rate used for the order before exporting to ShipStation.
			 *
			 * @param float    $exchange_rate The exchange rate to use for the order.
			 * @param WC_Order $order Order object.
			 *
			 * @since 4.3.7
			 */
			$exchange_rate = apply_filters( 'woocommerce_shipstation_export_exchange_rate', 1.00, $order );
			/**
			 * Filter whether order discounts should be exported as a separate line item to ShipStation.
			 *
			 * By default (true), discounts are exported as a separate line item. This has been the
			 * behavior since the beginning and is expected by all existing users and integrations.
			 *
			 * If set to false, the discount amount will instead be applied proportionally across the product line items,
			 * and no separate "Discount" line will be included in the export.
			 *
			 * ⚠️ Changing this behavior may break compatibility with external systems or workflows
			 * that rely on the presence of a separate discount line.
			 *
			 * This filter is provided to give developers flexibility in customizing how discounts
			 * are represented in the ShipStation export.
			 *
			 * @see   https://linear.app/a8c/issue/WOOSHIP-748/discounts-are-added-in-separate-line-item-as-total-discount-instead-of
			 * @see   https://github.com/woocommerce/woocommerce-shipstation/issues/85
			 *
			 * @param bool     $export_discounts_as_separate_item Whether to export discounts as a separate ShipStation line item. Default true.
			 * @param WC_Order $order                             The WooCommerce order object.
			 *
			 * @return bool Modified flag to control export behavior for discounts.
			 *
			 * @since 4.5.1
			 */
			$export_discounts_as_separate_item = apply_filters( 'woocommerce_shipstation_export_discounts_as_separate_item', true, $order );

			$order_xml              = $xml->createElement( 'Order' );
			$formatted_order_number = ltrim( $order->get_order_number(), '#' );
			$this->xml_append( $order_xml, 'OrderNumber', $formatted_order_number );
			$this->xml_append( $order_xml, 'OrderID', $order_id );

			// Sequence of date ordering: date paid > date completed > date created.
			$order_timestamp = $order->get_date_paid() ? $order->get_date_paid() : ( $order->get_date_completed() ? $order->get_date_completed() : $order->get_date_created() );
			$order_timestamp = $order_timestamp->getOffsetTimestamp();

			$order_timestamp -= $tz_offset;
			$order_status     = ( 'refunded' === $order->get_status() ) ? 'cancelled' : $order->get_status();
			$this->xml_append( $order_xml, 'OrderDate', gmdate( 'm/d/Y H:i', $order_timestamp ), false );
			$this->xml_append( $order_xml, 'OrderStatus', $order_status );
			$this->xml_append( $order_xml, 'PaymentMethod', $order->get_payment_method() );
			$this->xml_append( $order_xml, 'OrderPaymentMethodTitle', $order->get_payment_method_title() );
			$last_modified = strtotime( $order->get_date_modified()->date( 'm/d/Y H:i' ) ) - $tz_offset;
			$this->xml_append( $order_xml, 'LastModified', gmdate( 'm/d/Y H:i', $last_modified ), false );
			$this->xml_append( $order_xml, 'ShippingMethod', implode( ' | ', $this->get_shipping_methods( $order ) ) );

			$this->xml_append( $order_xml, 'CurrencyCode', $currency_code, false );

			$order_total = $order->get_total() - floatval( $order->get_total_refunded() );
			$tax_amount  = wc_round_tax_total( $order->get_total_tax() );

			// Maybe convert the order total and tax amount.
			if ( 1.00 !== $exchange_rate ) {
				$order_total = wc_format_decimal( ( $order_total * $exchange_rate ), wc_get_price_decimals() );
				$tax_amount  = wc_round_tax_total( $order->get_total_tax() * $exchange_rate );
			}

			$this->xml_append( $order_xml, 'OrderTotal', $order_total, false );
			$this->xml_append( $order_xml, 'TaxAmount', $tax_amount, false );

			if ( class_exists( 'WC_COG' ) ) {
				$wc_cog_order_total_cost = floatval( $order->get_meta( '_wc_cog_order_total_cost', true ) );

				// Maybe convert the order total cost of goods.
				if ( 1.00 !== $exchange_rate ) {
					$wc_cog_order_total_cost = $wc_cog_order_total_cost * $exchange_rate;
				}

				$this->xml_append( $order_xml, 'CostOfGoods', wc_format_decimal( $wc_cog_order_total_cost, wc_get_price_decimals() ), false );
			}

			$shipping_total = floatval( $order->get_shipping_total() );

			// Maybe convert the shipping total.
			if ( 1.00 !== $exchange_rate ) {
				$shipping_total = wc_format_decimal( ( $shipping_total * $exchange_rate ), wc_get_price_decimals() );
			}

			$this->xml_append( $order_xml, 'ShippingAmount', $shipping_total, false );
			$this->xml_append( $order_xml, 'CustomerNotes', $order->get_customer_note() );
			$this->xml_append( $order_xml, 'InternalNotes', implode( ' | ', $this->get_order_notes( $order ) ) );

			// Maybe append the gift and gift message XML element.
			if ( class_exists( 'WooCommerce\Shipping\ShipStation\Checkout' ) && $order->get_meta( Checkout::get_block_prefixed_meta_key( 'is_gift' ) ) ) {
				$this->xml_append( $order_xml, 'Gift', 'true', false );

				$gift_message = $order->get_meta( Checkout::get_block_prefixed_meta_key( 'gift_message' ) );

				if ( ! empty( $gift_message ) ) {
					$this->xml_append( $order_xml, 'GiftMessage', wp_specialchars_decode( $gift_message ) );
				}
			}

			// Custom fields - 1 is used for coupon codes.
			$this->xml_append( $order_xml, 'CustomField1', implode( ' | ', $order->get_coupon_codes() ) );

			// Custom fields 2 and 3 can be mapped to a custom field via the following filters.

			/**
			 * Custom fields 2 can be mapped to a custom field via the following filters.
			 *
			 * @since 4.0.1
			 */
			$meta_key = apply_filters( 'woocommerce_shipstation_export_custom_field_2', '' );
			if ( $meta_key ) {
				/**
				 * Allowing third party to modify the custom field 2 value.
				 *
				 * @since 4.1.0
				 */
				$this->xml_append( $order_xml, 'CustomField2', apply_filters( 'woocommerce_shipstation_export_custom_field_2_value', $order->get_meta( $meta_key, true ), $order_id ) );
			}

			/**
			 * Custom fields 3 can be mapped to a custom field via the following filters.
			 *
			 * @since 4.0.1
			 */
			$meta_key = apply_filters( 'woocommerce_shipstation_export_custom_field_3', '' );
			if ( $meta_key ) {
				/**
				 * Allowing third party to modify the custom field 3 value.
				 *
				 * @since 4.1.0
				 */
				$this->xml_append( $order_xml, 'CustomField3', apply_filters( 'woocommerce_shipstation_export_custom_field_3_value', $order->get_meta( $meta_key, true ), $order_id ) );
			}

			// Customer data.
			$customer_xml = $xml->createElement( 'Customer' );
			$this->xml_append( $customer_xml, 'CustomerCode', $order->get_billing_email() );

			$billto_xml = $xml->createElement( 'BillTo' );
			$this->xml_append( $billto_xml, 'Name', $order->get_billing_first_name() . ' ' . $order->get_billing_last_name() );
			$this->xml_append( $billto_xml, 'Company', $order->get_billing_company() );
			$this->xml_append( $billto_xml, 'Phone', $order->get_billing_phone() );
			$this->xml_append( $billto_xml, 'Email', $order->get_billing_email() );
			$customer_xml->appendChild( $billto_xml );

			$shipto_xml   = $xml->createElement( 'ShipTo' );
			$address_data = $this->get_address_data( $order );

			$this->xml_append( $shipto_xml, 'Name', $address_data['name'] );
			$this->xml_append( $shipto_xml, 'Company', $address_data['company'] );
			$this->xml_append( $shipto_xml, 'Address1', $address_data['address1'] );
			$this->xml_append( $shipto_xml, 'Address2', $address_data['address2'] );
			$this->xml_append( $shipto_xml, 'City', $address_data['city'] );
			$this->xml_append( $shipto_xml, 'State', $address_data['state'] );
			$this->xml_append( $shipto_xml, 'PostalCode', $address_data['postcode'] );
			$this->xml_append( $shipto_xml, 'Country', $address_data['country'] );
			$this->xml_append( $shipto_xml, 'Phone', $address_data['phone'] );

			$customer_xml->appendChild( $shipto_xml );

			$order_xml->appendChild( $customer_xml );

			// Item data.
			$found_item         = false;
			$product_dimensions = array();
			$items_xml          = $xml->createElement( 'Items' );
			// Merge arrays without loosing indexes.
			$order_items = $order->get_items() + $order->get_items( 'fee' );
			foreach ( $order_items as $item_id => $item ) {
				$product                = is_callable( array( $item, 'get_product' ) ) ? $item->get_product() : false;
				$item_needs_no_shipping = ! $product || ! $product->needs_shipping();
				$item_not_a_fee         = 'fee' !== $item->get_type();

				/**
				 * Allow third party to exclude the item for when an item does not need shipping or is a fee.
				 *
				 * @since 4.1.31
				 */
				if ( apply_filters( 'woocommerce_shipstation_no_shipping_item', $item_needs_no_shipping && $item_not_a_fee, $product, $item ) ) {
					continue;
				}

				$found_item = true;
				$item_xml   = $xml->createElement( 'Item' );
				$this->xml_append( $item_xml, 'LineItemID', $item_id );

				if ( 'fee' === $item->get_type() ) {
					$this->xml_append( $item_xml, 'Name', $item->get_name() );
					$this->xml_append( $item_xml, 'Quantity', 1, false );

					$item_total = $order->get_item_total( $item, false, true );

					// Maybe convert fee item total.
					if ( 1.00 !== $exchange_rate ) {
						$item_total = wc_format_decimal( ( $item_total * $exchange_rate ), wc_get_price_decimals() );
					}

					$this->xml_append( $item_xml, 'UnitPrice', $item_total, false );
				}

				// handle product specific data.
				if ( $product && $product->needs_shipping() ) {
					$this->xml_append( $item_xml, 'SKU', $product->get_sku() );
					$this->xml_append( $item_xml, 'Name', $item->get_name() );
					// image data.
					$image_id  = $product->get_image_id();
					$image_src = $image_id ? wp_get_attachment_image_src( $image_id, 'woocommerce_gallery_thumbnail' ) : '';
					$image_url = is_array( $image_src ) ? current( $image_src ) : '';

					$this->xml_append( $item_xml, 'ImageUrl', $image_url );

					if ( 'kg' === $store_weight_unit ) {
						$this->xml_append( $item_xml, 'Weight', wc_get_weight( $product->get_weight(), 'g' ), false );
						$this->xml_append( $item_xml, 'WeightUnits', 'Grams', false );
					} else {
						$this->xml_append( $item_xml, 'Weight', $product->get_weight(), false );
						$this->xml_append( $item_xml, 'WeightUnits', $this->get_shipstation_weight_units( $store_weight_unit ), false );
					}

					// current item quantity - refunded quantity.
					$item_qty = $item->get_quantity() - abs( $order->get_qty_refunded_for_item( $item_id ) );
					$this->xml_append( $item_xml, 'Quantity', $item_qty, false );

					$item_total = $export_discounts_as_separate_item ? $order->get_item_subtotal( $item, false, true ) : $order->get_item_total( $item, false, true );

					// Maybe convert item total.
					if ( 1.00 !== $exchange_rate ) {
						$item_total = wc_format_decimal( ( $item_total * $exchange_rate ), wc_get_price_decimals() );
					}

					$this->xml_append( $item_xml, 'UnitPrice', $item_total, false );

					$product_dimensions[] = array(
						'length' => wc_get_dimension( floatval( $product->get_length() ), 'in' ),
						'width'  => wc_get_dimension( floatval( $product->get_width() ), 'in' ),
						'height' => wc_get_dimension( floatval( $product->get_height() ), 'in' ),
						'qty'    => $item_qty,
					);
				}

				if ( $item->get_meta_data() ) {
					add_filter( 'woocommerce_is_attribute_in_product_name', '__return_false' );
					$formatted_meta = $item->get_formatted_meta_data();

					if ( ! empty( $formatted_meta ) ) {
						$options_xml = $xml->createElement( 'Options' );

						foreach ( $formatted_meta as $meta_key => $meta ) {
							$option_xml = $xml->createElement( 'Option' );
							$this->xml_append( $option_xml, 'Name', $meta->display_key );
							$this->xml_append( $option_xml, 'Value', wp_strip_all_tags( $meta->display_value ) );
							$options_xml->appendChild( $option_xml );
						}

						$item_xml->appendChild( $options_xml );
					}
				}

				$items_xml->appendChild( $item_xml );
			}

			if ( ! $found_item ) {
				continue;
			}

			// Get the first product's dimensions.
			$dimensions = array_shift( $product_dimensions );

			// Make sure the product item is only 1 and the quantity is also 1.
			if ( empty( $product_dimensions ) && ! empty( $dimensions['qty'] ) && 1 === $dimensions['qty'] ) {
				$dimensions_xml = $xml->createElement( 'Dimensions' );

				$this->xml_append( $dimensions_xml, 'Length', $dimensions['length'], false );
				$this->xml_append( $dimensions_xml, 'Width', $dimensions['width'], false );
				$this->xml_append( $dimensions_xml, 'Height', $dimensions['height'], false );
				$this->xml_append( $dimensions_xml, 'DimensionUnits', 'in', false );

				$order_xml->appendChild( $dimensions_xml );
			}

			// Append cart level discount line.
			if ( $export_discounts_as_separate_item && $order->get_total_discount() ) {
				$item_xml = $xml->createElement( 'Item' );
				$this->xml_append( $item_xml, 'SKU', 'total-discount' );
				$this->xml_append( $item_xml, 'Name', __( 'Total Discount', 'woocommerce-shipstation-integration' ) );
				$this->xml_append( $item_xml, 'Adjustment', 'true', false );
				$this->xml_append( $item_xml, 'Quantity', 1, false );

				$order_total_discount = $order->get_total_discount() * -1;

				// Maybe convert order total discount.
				if ( 1.00 !== $exchange_rate ) {
					$order_total_discount = wc_format_decimal( ( $order_total_discount * $exchange_rate ), wc_get_price_decimals() );
				}

				$this->xml_append( $item_xml, 'UnitPrice', $order_total_discount, false );
				$items_xml->appendChild( $item_xml );
			}

			// Append items XML.
			$order_xml->appendChild( $items_xml );

			/**
			 * Allow third party to modify the XML that will be exported.
			 *
			 * @since 4.1.39
			 */
			$orders_xml->appendChild( apply_filters( 'woocommerce_shipstation_export_order_xml', $order_xml ) );

			++$exported;

			// Add order note to indicate it has been exported to Shipstation.
			if ( 'yes' !== $order->get_meta( '_shipstation_exported', true ) ) {
				$order->add_order_note( __( 'Order has been exported to Shipstation', 'woocommerce-shipstation-integration' ) );
				$order->update_meta_data( '_shipstation_exported', 'yes' );
				$order->save_meta_data();
			}
		}

		$orders_xml->setAttribute( 'page', $page );
		$orders_xml->setAttribute( 'pages', ceil( $max_results / WC_SHIPSTATION_EXPORT_LIMIT ) );
		$xml->appendChild( $orders_xml );
		echo $xml->saveXML(); //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped, we want the output to be XML.

		/* translators: 1: total count */
		$this->log( sprintf( __( 'Exported %s orders', 'woocommerce-shipstation-integration' ), $exported ) );
		// phpcs:enable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
	}

	/**
	 * Get address data from Order.
	 *
	 * @param WC_Order $order Order object.
	 *
	 * @result array.
	 */
	public function get_address_data( $order ) {
		$shipping_country = $order->get_shipping_country();
		$shipping_address = $order->get_shipping_address_1();

		$address = array();

		if ( empty( $shipping_country ) && empty( $shipping_address ) ) {
			$name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();

			$address['name']     = $name;
			$address['company']  = $order->get_billing_company();
			$address['address1'] = $order->get_billing_address_1();
			$address['address2'] = $order->get_billing_address_2();
			$address['city']     = $order->get_billing_city();
			$address['state']    = $order->get_billing_state();
			$address['postcode'] = $order->get_billing_postcode();
			$address['country']  = $order->get_billing_country();
			$address['phone']    = $order->get_billing_phone();
		} else {
			$name = $order->get_shipping_first_name() . ' ' . $order->get_shipping_last_name();

			$address['name']     = $name;
			$address['company']  = $order->get_shipping_company();
			$address['address1'] = $order->get_shipping_address_1();
			$address['address2'] = $order->get_shipping_address_2();
			$address['city']     = $order->get_shipping_city();
			$address['state']    = $order->get_shipping_state();
			$address['postcode'] = $order->get_shipping_postcode();
			$address['country']  = $order->get_shipping_country();
			$address['phone']    = $order->get_billing_phone();
		}

		/**
		 * Allow third party to modify the address data.
		 *
		 * @param array    $address Address data.
		 * @param WC_Order $order Order object.
		 * @param boolean  $is_export_address Flag to export address data or not.
		 *
		 * @since 4.2.0
		 */
		return apply_filters( 'woocommerce_shipstation_export_address_data', $address, $order, true );
	}

	/**
	 * Get shipping method names
	 *
	 * @param WC_Order $order Order object.
	 *
	 * @return array
	 */
	private function get_shipping_methods( $order ) {
		$shipping_methods      = $order->get_shipping_methods();
		$shipping_method_names = array();

		foreach ( $shipping_methods as $shipping_method ) {
			// Replace non-AlNum characters with space.
			$method_name             = preg_replace( '/[^A-Za-z0-9 \-\.\_,]/', '', $shipping_method['name'] );
			$shipping_method_names[] = $method_name;
		}

		return $shipping_method_names;
	}

	/**
	 * Get Order Notes
	 *
	 * @param  WC_Order $order Order object.
	 * @return array
	 */
	private function get_order_notes( $order ) {
		$args = array(
			'post_id' => $order->get_id(),
			'approve' => 'approve',
			'type'    => 'order_note',
		);

		remove_filter( 'comments_clauses', array( 'WC_Comments', 'exclude_order_comments' ), 10 );
		$notes = get_comments( $args );
		add_filter( 'comments_clauses', array( 'WC_Comments', 'exclude_order_comments' ), 10, 1 );

		$order_notes = array();

		foreach ( $notes as $note ) {
			if ( 'WooCommerce' !== $note->comment_author ) {
				$order_notes[] = $note->comment_content;
			}
		}

		return $order_notes;
	}

	/**
	 * Append XML as cdata.
	 *
	 * @param DOMElement $append_to XML DOMElement to append to.
	 * @param string     $name      Element name.
	 * @param mixed      $value     Element value.
	 * @param boolean    $cdata     Using cData or not.
	 */
	private function xml_append( $append_to, $name, $value, $cdata = true ) {
		// phpcs:disable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase --- ownerDocument is the correct property.
		$data = $append_to->appendChild( $append_to->ownerDocument->createElement( $name ) );

		if ( $cdata ) {
			$child_node = empty( $append_to->ownerDocument->createCDATASection( $value ) ) ? $append_to->ownerDocument->createCDATASection( '' ) : $append_to->ownerDocument->createCDATASection( $value );
		} else {
			$child_node = empty( $append_to->ownerDocument->createTextNode( $value ) ) ? $append_to->ownerDocument->createTextNode( '' ) : $append_to->ownerDocument->createTextNode( $value );
		}

		$data->appendChild( $child_node );
		// phpcs:enable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase
	}

	/**
	 * Convert weight unit abbreviation to Shipstation enum (Pounds, Ounces, Grams).
	 *
	 * @param string $unit_abbreviation Weight unit abbreviation.
	 */
	private function get_shipstation_weight_units( $unit_abbreviation ) {
		switch ( $unit_abbreviation ) {
			case 'lbs':
				return 'Pounds';
			case 'oz':
				return 'Ounces';
			case 'g':
				return 'Grams';
			default:
				return $unit_abbreviation;
		}
	}
}

return new WC_Shipstation_API_Export();
</file>

<file path="includes/class-wc-shipstation-privacy.php">
<?php
/**
 * Class WC_ShipStation_Privacy file.
 *
 * @package WC_ShipStation
 */

if ( ! class_exists( 'WC_Abstract_Privacy' ) ) {
	return;
}

/**
 * A class to maintain privacy.
 *
 * @package WC_ShipStation
 */
class WC_ShipStation_Privacy extends WC_Abstract_Privacy {
	/**
	 * Constructor
	 */
	public function __construct() {
		parent::__construct( __( 'ShipStation', 'woocommerce-shipstation-integration' ) );

		$this->add_exporter( 'woocommerce-shipstation-order-data', __( 'WooCommerce ShipStation Order Data', 'woocommerce-shipstation-integration' ), array( $this, 'order_data_exporter' ) );

		$this->add_eraser( 'woocommerce-shipstation-order-data', __( 'WooCommerce ShipStation Data', 'woocommerce-shipstation-integration' ), array( $this, 'order_data_eraser' ) );
	}

	/**
	 * Returns a list of orders.
	 *
	 * @param string $email_address Email address.
	 * @param int    $page Current page number in pagination.
	 *
	 * @return WC_Order[]
	 */
	protected function get_orders( $email_address, $page ) {
		$user = get_user_by( 'email', $email_address ); // Check if user has an ID in the DB to load stored personal data.

		$order_query = array(
			'limit' => 10,
			'page'  => $page,
		);

		if ( $user instanceof WP_User ) {
			$order_query['customer_id'] = (int) $user->ID;
		} else {
			$order_query['billing_email'] = $email_address;
		}

		return wc_get_orders( $order_query );
	}

	/**
	 * Gets the message of the privacy to display.
	 */
	public function get_privacy_message() {
		/* translators: 1: URL to documentation */
		return wpautop( sprintf( __( 'By using this extension, you may be storing personal data or sharing data with an external service. <a href="%s" target="_blank">Learn more about how this works, including what you may want to include in your privacy policy.</a>', 'woocommerce-shipstation-integration' ), 'https://docs.woocommerce.com/document/privacy-shipping/#woocommerce-shipstation' ) );
	}

	/**
	 * Handle exporting data for Orders.
	 *
	 * @param string $email_address E-mail address to export.
	 * @param int    $page          Pagination of data.
	 *
	 * @return array
	 */
	public function order_data_exporter( $email_address, $page = 1 ) {
		$done           = false;
		$data_to_export = array();

		$orders = $this->get_orders( $email_address, (int) $page );

		$done = true;

		if ( 0 < count( $orders ) ) {
			foreach ( $orders as $order ) {
				$data_to_export[] = array(
					'group_id'    => 'woocommerce_orders',
					'group_label' => __( 'Orders', 'woocommerce-shipstation-integration' ),
					'item_id'     => 'order-' . $order->get_id(),
					'data'        => array(
						array(
							'name'  => __( 'ShipStation tracking provider', 'woocommerce-shipstation-integration' ),
							'value' => $order->get_meta( '_tracking_provider', true ),
						),
						array(
							'name'  => __( 'ShipStation tracking number', 'woocommerce-shipstation-integration' ),
							'value' => $order->get_meta( '_tracking_number', true ),
						),
						array(
							'name'  => __( 'ShipStation date shipped', 'woocommerce-shipstation-integration' ),
							'value' => $order->get_meta( '_date_shipped', true ) ? wp_date( 'Y-m-d H:i:s', $order->get_meta( '_date_shipped', true ) ) : '',
						),
					),
				);
			}

			$done = 10 > count( $orders );
		}

		return array(
			'data' => $data_to_export,
			'done' => $done,
		);
	}

	/**
	 * Finds and erases order data by email address.
	 *
	 * @since 3.4.0
	 * @param string $email_address The user email address.
	 * @param int    $page  Page.
	 * @return array An array of personal data in name value pairs
	 */
	public function order_data_eraser( $email_address, $page ) {
		$orders = $this->get_orders( $email_address, (int) $page );

		$items_removed  = false;
		$items_retained = false;
		$messages       = array();

		foreach ( (array) $orders as $order ) {
			$order = wc_get_order( $order->get_id() );

			list( $removed, $retained, $msgs ) = $this->maybe_handle_order( $order );

			$items_removed  |= $removed;
			$items_retained |= $retained;
			$messages        = array_merge( $messages, $msgs );
		}

		// Tell core if we have more orders to work on still.
		$done = count( $orders ) < 10;

		return array(
			'items_removed'  => $items_removed,
			'items_retained' => $items_retained,
			'messages'       => $messages,
			'done'           => $done,
		);
	}

	/**
	 * Handle eraser of data tied to Orders
	 *
	 * @param WC_Order $order Order object.
	 *
	 * @return array
	 */
	protected function maybe_handle_order( $order ) {
		$item_count           = $order->get_meta( '_shipstation_shipped_item_count', true );
		$tracking_provider    = $order->get_meta( '_tracking_provider', true );
		$tracking_number      = $order->get_meta( '_tracking_number', true );
		$date_shipped         = $order->get_meta( '_date_shipped', true );
		$shipstation_exported = $order->get_meta( '_shipstation_exported', true );

		if ( empty( $item_count ) && empty( $tracking_provider ) && empty( $tracking_number )
			&& empty( $date_shipped ) && empty( $shipstation_exported ) ) {
			return array( false, false, array() );
		}

		$order->delete_meta_data( '_shipstation_shipped_item_count' );
		$order->delete_meta_data( '_tracking_provider' );
		$order->delete_meta_data( '_tracking_number' );
		$order->delete_meta_data( '_date_shipped' );
		$order->delete_meta_data( '_shipstation_exported' );
		$order->save_meta_data();

		return array( true, false, array( __( 'ShipStation Order Data Erased.', 'woocommerce-shipstation-integration' ) ) );
	}
}
</file>

<file path="README.md">
[![CI](https://github.com/woocommerce/woocommerce-shipstation/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipstation/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-shipstation/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipstation/actions/workflows/cron_qit.yml)

woocommerce-shipstation
====================

Ship your WooCommerce orders with confidence, save on top carriers, and automate your processes with ShipStation.

## NPM Scripts

WooCommerce Shipstation utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These may include building JavaScript, SASS, CSS minification, and language files.
</file>

<file path="includes/class-checkout.php">
<?php
/**
 * Class WC_ShipStation_Checkout file.
 *
 * @package WC_ShipStation
 */

namespace WooCommerce\Shipping\ShipStation;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use Exception;
use WC_Cart;
use WC_Order;
use WP_Error;
use WP_HTML_Tag_Processor;
use Automattic\WooCommerce\Blocks\Domain\Services\CheckoutFields;
use WC_ShipStation_Integration;

/**
 * Checkout Class
 */
class Checkout {

	/**
	 * The namespace for our checkout fields.
	 *
	 * @var string
	 */
	const FIELD_NAMESPACE = 'woocommerce_shipstation';

	/**
	 * Maximum length of the gift message.
	 *
	 * @var int
	 */
	public int $gift_message_max_length;

	/**
	 * Constructor method.
	 */
	public function __construct() {
		add_filter( 'woocommerce_checkout_fields', array( $this, 'add_gift_fields_to_classic_checkout' ) );
		add_filter( 'woocommerce_form_field_checkbox', array( $this, 'modify_optional_label' ), 10, 2 );
		add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
		add_action( 'woocommerce_checkout_process', array( $this, 'validate_classic_checkout_gift_fields' ) );
		add_action( 'woocommerce_checkout_update_order_meta', array( $this, 'save_gift_field_values_to_classic_checkout_order_meta' ), 10, 1 );
		add_filter( 'woocommerce_admin_shipping_fields', array( $this, 'maybe_display_gift_data_below_admin_shipping_fields' ), 15, 3 );
		add_action( 'woocommerce_thankyou', array( $this, 'maybe_display_order_gift_data_for_customers' ) );
		add_action( 'woocommerce_init', array( $this, 'add_gift_fields_to_block_checkout' ) );
		add_filter( 'woocommerce_filter_fields_for_order_confirmation', array( $this, 'filter_fields_for_order_confirmation' ), 10, 2 );
		add_action( 'woocommerce_shipping_init', array( $this, 'maybe_deregister_fields' ) );
		add_action( 'woocommerce_blocks_validate_location_order_fields', array( $this, 'validate_block_checkout_gift_fields' ), 10, 2 );
		add_action( 'woocommerce_store_api_checkout_update_order_from_request', array( $this, 'maybe_delete_order_gift_field_meta_data' ) );
		add_action( 'woocommerce_store_api_checkout_update_order_meta', array( $this, 'delete_sessions' ), 15 );
		add_action( 'woocommerce_email_customer_details', array( $this, 'display_gift_fields_in_order_email' ), 40, 3 );

		// AJAX handlers for persisting gift field values.
		add_action( 'wp_ajax_shipstation_save_field_value', array( $this, 'ajax_save_field_value' ) );
		add_action( 'wp_ajax_nopriv_shipstation_save_field_value', array( $this, 'ajax_save_field_value' ) );
		add_action( 'wp_ajax_shipstation_load_field_values', array( $this, 'ajax_load_field_values' ) );
		add_action( 'wp_ajax_nopriv_shipstation_load_field_values', array( $this, 'ajax_load_field_values' ) );

		/**
		 * We'll set the default gift message max length to 255 characters, allowing merchants to override it, while
		 * enforcing a minimum of 1 and a maximum of 1000 characters.
		 *
		 * ShipStation allows a maximum of 1000 characters for the gift message. 1 is the shortest "usable" length.
		 *
		 * @since 4.7.0
		 */
		$this->gift_message_max_length = min( max( intval( apply_filters( 'woocommerce_shipstation_gift_message_max_length', 255 ) ), 1 ), 1000 );
	}

	/**
	 * Enqueue checkout scripts and styles.
	 *
	 * @return void
	 */
	public function enqueue_scripts(): void {
		if ( ! WC_ShipStation_Integration::$gift_enabled ) {
			return;
		}

		if ( ! self::cart_needs_shipping() ) {
			return;
		}

		if ( self::is_block_checkout() ) {
			wp_enqueue_style( 'shipstation-block-checkout', WC_SHIPSTATION_PLUGIN_URL . 'assets/css/block-checkout.css', array(), WC_SHIPSTATION_VERSION );
			wp_add_inline_style( 'shipstation-block-checkout', $this->display_gift_field_descriptions_on_block_checkout() );
		}

		if ( self::is_classic_checkout() ) {
			wp_enqueue_style( 'shipstation-classic-checkout', WC_SHIPSTATION_PLUGIN_URL . 'assets/css/classic-checkout.css', array(), WC_SHIPSTATION_VERSION );

			$suffix = defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ? '' : '.min';

			wp_enqueue_script( 'wc-shipstation-checkout', WC_SHIPSTATION_PLUGIN_URL . 'assets/js/checkout' . $suffix . '.js', array( 'wc-checkout' ), WC_SHIPSTATION_VERSION, true );

			wp_localize_script(
				'wc-shipstation-checkout',
				'wc_shipstation_checkout_params',
				array(
					'field_ids' => array(
						'is_gift'      => WC_ShipStation_Integration::$order_meta_keys['is_gift'],
						'gift_message' => WC_ShipStation_Integration::$order_meta_keys['gift_message'],
					),
				)
			);
		}
	}

	/**
	 * Get a list of gift fields.
	 *
	 * @return array
	 */
	protected function get_gift_fields(): array {
		return array(
			array(
				'id'                         => WC_ShipStation_Integration::$order_meta_keys['is_gift'],
				'type'                       => 'checkbox',
				'class'                      => array( 'woocommerce-shipstation-gift', 'shipstation-is-gift' ),
				'label'                      => __( 'Send as gift', 'woocommerce-shipstation-integration' ),
				'optionalLabel'              => __( 'Mark order as a gift', 'woocommerce-shipstation-integration' ),
				'description'                => __( 'Gift orders include an optional personal message on the packing slip.', 'woocommerce-shipstation-integration' ),
				'truncated_value'            => false,
				'required'                   => false,
				'show_in_order_confirmation' => true,
				'attributes'                 => array(
					// translators: Aria-label for the gift message checkbox.
					'aria-label' => __( 'Mark order as a gift. When enabled order will include an optional personal message on the packing slip.', 'woocommerce-shipstation-integration' ),
				),
			),
			array(
				'id'                         => WC_ShipStation_Integration::$order_meta_keys['gift_message'],
				'type'                       => 'text',
				'class'                      => array( 'woocommerce-shipstation-gift', 'shipstation-gift-message' ),
				'label'                      => __( 'Gift message', 'woocommerce-shipstation-integration' ),
				'optionalLabel'              => __( 'Add gift message', 'woocommerce-shipstation-integration' ),
				// translators: %1$d is the maximum length of the gift message.
				'description'                => sprintf( __( 'Use the space above to enter your gift message. Approx., %1$d characters.', 'woocommerce-shipstation-integration' ), $this->gift_message_max_length ),
				'truncated_value'            => true,
				'placeholder'                => esc_attr__( 'Message for the gift.', 'woocommerce-shipstation-integration' ),
				'attributes'                 => array(
					'maxLength'  => $this->gift_message_max_length,
					// translators: %1$d is the maximum length of the gift message.
					'aria-label' => sprintf( __( 'Gift message, optional. Use this field to enter your gift message. Approximately %1$d characters.', 'woocommerce-shipstation-integration' ), $this->gift_message_max_length ),
				),
				'required'                   => false,
				'sanitize_callback'          => function ( $value ) {
					return sanitize_textarea_field( $value );
				},
				'show_in_order_confirmation' => true,
				'desc_tip'                   => true,
			),
		);
	}

	/**
	 * Add gift fields to the classic checkout form.
	 *
	 * @param array $checkout_fields List of checkout fields.
	 *
	 * @return array Modified list of checkout fields including gift fields if enabled.
	 */
	public function add_gift_fields_to_classic_checkout( array $checkout_fields ): array {
		if ( ! WC_ShipStation_Integration::$gift_enabled || ! self::cart_needs_shipping() ) {
			return $checkout_fields;
		}

		foreach ( $this->get_gift_fields() as $gift_field ) {
			$checkout_fields['order'][ $gift_field['id'] ]          = $gift_field;
			$checkout_fields['order'][ $gift_field['id'] ]['label'] = $gift_field['optionalLabel'];
			if ( ! empty( $gift_field['attributes']['maxLength'] ) ) {
				$checkout_fields['order'][ $gift_field['id'] ]['maxlength'] = $gift_field['attributes']['maxLength'];
			}
		}

		return $checkout_fields;
	}

	/**
	 * Hide (optional) text in checkbox label.
	 *
	 * @param string $field Field HTML.
	 * @param string $key   Field key.
	 *
	 * @return string
	 */
	public function modify_optional_label( string $field, string $key ): string {
		if ( 'shipstation_is_gift' !== $key ) {
			return $field;
		}

		$p = new WP_HTML_Tag_Processor( $field );

		while ( $p->next_tag( 'span' ) ) {
			if ( $p->get_attribute( 'class' ) === 'optional' ) {
				$p->add_class( 'screen-reader-text' );
				break;
			}
		}

		return $p->get_updated_html();
	}

	/**
	 * Add gift fields to the block checkout form.
	 *
	 * @return void
	 */
	public function add_gift_fields_to_block_checkout(): void {
		if ( ! WC_ShipStation_Integration::$gift_enabled ) {
			return;
		}

		foreach ( $this->get_gift_fields() as $gift_field ) {
			try {
				$label = ( 'checkbox' !== $gift_field['type'] && false === $gift_field['required'] ) ? $gift_field['optionalLabel'] . ' (' . __( 'optional', 'woocommerce-shipstation-integration' ) . ')' : $gift_field['optionalLabel'];
				woocommerce_register_additional_checkout_field(
					array_merge(
						$gift_field,
						array(
							'id'            => self::get_namespaced_field_key( $gift_field['id'] ),
							'optionalLabel' => $label,
							'location'      => 'order',
							'placeholder'   => '',
						)
					)
				);
			} catch ( Exception $e ) {
				// Log error silently.
				wc_get_logger()->error( $e->getMessage(), array( 'source' => 'woocommerce-shipstation' ) );
			}
		}
	}

	/**
	 * Deregister gift fields when they are not needed.
	 *
	 * This method removes gift fields from the block checkout in the following cases:
	 * - When the cart doesn't need shipping
	 * - On order-received page when the order is not a gift
	 * - On view-order page when the order is not a gift
	 *
	 * @return void
	 */
	public function maybe_deregister_fields() {
		// Early return if the deregister function doesn't exist.
		if ( ! function_exists( '__internal_woocommerce_blocks_deregister_checkout_field' ) ) {
			return;
		}

		// Don't deregister fields in admin or on irrelevant frontend pages.
		if ( is_admin() || ! self::is_checkout() ) {
			return;
		}

		// Keep fields if we're on a regular checkout page and the cart needs shipping.
		if ( self::cart_needs_shipping() ) {
			return;
		}

		// Handle relevant Woo endpoints.
		if ( is_wc_endpoint_url( 'order-received' ) ) {
			$order_key = isset( $_GET['key'] ) ? sanitize_text_field( wp_unslash( $_GET['key'] ) ) : ''; // phpcs:ignore WordPress.Security.NonceVerification.Recommended --- Nonce is not required here.
			if ( ! empty( $order_key ) ) {
				$order_id = wc_get_order_id_by_order_key( $order_key );
				if ( ! empty( $order_id ) && $this->is_order_a_gift( $order_id ) ) {
					return;
				}
			}
		} elseif ( is_wc_endpoint_url( 'view-order' ) ) {
			$order_id = absint( get_query_var( 'view-order' ) );
			if ( ! empty( $order_id ) && $this->is_order_a_gift( $order_id ) ) {
				return;
			}
		}

		// If we've reached this point, deregister all gift fields.
		try {
			foreach ( $this->get_gift_fields() as $gift_field ) {
				__internal_woocommerce_blocks_deregister_checkout_field(
					self::get_namespaced_field_key( $gift_field['id'] )
				);
			}
		} catch ( Exception $e ) {
			wc_get_logger()->error(
				sprintf( 'Error deregistering gift fields: %s', $e->getMessage() ),
				array( 'source' => 'woocommerce-shipstation' )
			);
		}
	}

	/**
	 * Is the order a gift?
	 *
	 * Checks if the order is marked as a gift.
	 *
	 * @param int $order_id The order ID.
	 *
	 * @return bool
	 */
	private function is_order_a_gift( int $order_id ): bool {
		$order = wc_get_order( $order_id );
		if ( empty( $order ) ) {
			return false;
		}

		return (bool) $order->get_meta( self::get_block_prefixed_meta_key( 'is_gift' ) );
	}

	/**
	 * Display gift field descriptions on the block checkout form by injecting CSS styles.
	 *
	 * Generates CSS rules that use custom properties to display description text
	 * below each gift-related field in the block checkout form. This allows the
	 * descriptions to be styled and positioned consistently while maintaining
	 * proper spacing.
	 *
	 * @return string CSS rules for displaying gift field descriptions, with each rule
	 *                targeting a specific gift field class and setting its description text.
	 */
	private function display_gift_field_descriptions_on_block_checkout(): string {
		ob_start();
		foreach ( $this->get_gift_fields() as $gift_field ) {
			$css_class   = self::get_css_class_field_key( $gift_field['id'] );
			$description = isset( $gift_field['description'] ) ? $gift_field['description'] : '';
			echo '
			.wc-block-components-address-form__', esc_attr( $css_class ), ' {
				--description-text: "', esc_attr( $description ), '";
			}';
		}

		return ob_get_clean();
	}

	/**
	 * Validates the gift fields during checkout.
	 *
	 * Checks if the gift message length is within allowed limits when an order is marked as a gift.
	 * Returns validation error details if the message exceeds the maximum characters.
	 *
	 * @param boolean $is_gift      Whether the order is marked as a gift.
	 * @param string  $gift_message Optional gift message text.
	 *
	 * @return array Empty array if validation passes, or array with error details.
	 */
	protected function validate_gift_fields( bool $is_gift, string $gift_message ): array {
		if ( $is_gift && strlen( $gift_message ) > $this->gift_message_max_length ) {
			return array(
				'id'   => 'shipstation_exceeded_gift_message',
				'text' => sprintf(
					// translators: %1$d is the maximum length of the gift message.
					__( 'Please ensure the gift message does not exceed %d characters.', 'woocommerce-shipstation-integration' ),
					$this->gift_message_max_length
				),
			);
		}

		return array();
	}

	/**
	 * Validates gift fields during block checkout validation.
	 *
	 * @param WP_Error $errors WordPress error object to add validation errors.
	 * @param array    $fields List of checkout fields.
	 *
	 * @return void
	 */
	public function validate_block_checkout_gift_fields( WP_Error $errors, array $fields ): void {
		if ( ! WC_ShipStation_Integration::$gift_enabled || ! self::cart_needs_shipping() ) {
			return;
		}

		$is_gift_field_id      = self::get_namespaced_field_key( 'is_gift' );
		$gift_message_field_id = self::get_namespaced_field_key( 'gift_message' );

		$is_gift_value      = isset( $fields[ $is_gift_field_id ] ) ? boolval( $fields[ $is_gift_field_id ] ) : false;
		$gift_message_value = isset( $fields[ $gift_message_field_id ] ) ? $fields[ $gift_message_field_id ] : '';

		$validation_results = $this->validate_gift_fields( $is_gift_value, $gift_message_value );

		if ( ! empty( $validation_results ) ) {
			$errors->add( $validation_results['id'], $validation_results['text'] );
		}
	}

	/**
	 * Deletes gift-related metadata from an order if the order is not marked as a gift.
	 * This helps keep the order metadata clean by removing unnecessary gift fields when they are not being used.
	 *
	 * @param WC_Order $order WooCommerce Order object to check and potentially clean gift metadata from.
	 *
	 * @return void
	 */
	public function maybe_delete_order_gift_field_meta_data( WC_Order $order ): void {
		$is_gift_meta_key = self::get_block_prefixed_meta_key( 'is_gift' );
		$is_gift_value    = $order->get_meta( $is_gift_meta_key );

		if ( empty( $is_gift_value ) || ! self::cart_needs_shipping() ) {
			$this->delete_order_gift_field_meta_data( $order );
		}
	}

	/**
	 * Deletes gift-related metadata from an order.
	 *
	 * @param WC_Order $order WooCommerce Order object.
	 *
	 * @return void
	 */
	public function delete_order_gift_field_meta_data( WC_Order $order ): void {
		$is_gift_meta_key      = self::get_block_prefixed_meta_key( 'is_gift' );
		$gift_message_meta_key = self::get_block_prefixed_meta_key( 'gift_message' );

		$order->delete_meta_data( $is_gift_meta_key );
		$order->delete_meta_data( $gift_message_meta_key );
	}

	/**
	 * Validates gift fields during classic checkout validation.
	 *
	 * @return void
	 */
	public function validate_classic_checkout_gift_fields(): void {
		// phpcs:disable WordPress.Security.NonceVerification.Missing --- Nonce is not required here.
		$is_gift      = isset( $_POST[ WC_ShipStation_Integration::$order_meta_keys['is_gift'] ] ) ? (bool) $_POST[ WC_ShipStation_Integration::$order_meta_keys['is_gift'] ] : false; //phpcs:ignore
		$gift_message = isset( $_POST[ WC_ShipStation_Integration::$order_meta_keys['gift_message'] ] ) ? sanitize_textarea_field( wp_unslash( $_POST[ WC_ShipStation_Integration::$order_meta_keys['gift_message'] ] ) ) : '';
		// phpcs:enable WordPress.Security.NonceVerification.Missing

		$validation_results = $this->validate_gift_fields( $is_gift, $gift_message );
		if ( ! empty( $validation_results ) ) {
			wc_add_notice( $validation_results['text'], 'error' );
		}
	}

	/**
	 * Saves gift-related field values submitted during classic checkout to order meta data.
	 *
	 * @param int $order_id Order ID.
	 *
	 * @return void
	 */
	public function save_gift_field_values_to_classic_checkout_order_meta( int $order_id ): void {
		if ( ! WC_ShipStation_Integration::$gift_enabled || ! self::cart_needs_shipping() ) {
			return;
		}

		// phpcs:disable WordPress.Security.NonceVerification.Missing --- Nonce is not required here.
		$is_gift      = isset( $_POST[ WC_ShipStation_Integration::$order_meta_keys['is_gift'] ] ) ? (bool) sanitize_text_field( wp_unslash( $_POST[ WC_ShipStation_Integration::$order_meta_keys['is_gift'] ] ) ) : false;
		$gift_message = isset( $_POST[ WC_ShipStation_Integration::$order_meta_keys['gift_message'] ] ) ? $this->sanitize_gift_message( wp_unslash( $_POST[ WC_ShipStation_Integration::$order_meta_keys['gift_message'] ] ) ) : ''; // phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
		// phpcs:enable WordPress.Security.NonceVerification.Missing

		if ( ! $is_gift ) {
			return;
		}

		$order = wc_get_order( $order_id );
		if ( ! $order instanceof WC_Order ) {
			return;
		}

		$order->update_meta_data( self::get_block_prefixed_meta_key( 'is_gift' ), true );

		if ( ! empty( $gift_message ) ) {
			$order->update_meta_data( self::get_block_prefixed_meta_key( 'gift_message' ), substr( $gift_message, 0, $this->gift_message_max_length ) );
		}

		$order->save_meta_data();

		// Clear the session data after saving to the order.
		$this->delete_sessions();
	}

	/**
	 * Gets formatted gift data from an order.
	 *
	 * Retrieves and formats gift-related data from order meta, including
	 * - Gift status (whether order is marked as gift)
	 * - Gift message if present
	 * - Formatted values appropriate for admin or frontend display
	 *
	 * @param WC_Order $order   WooCommerce order object to get gift data from.
	 *
	 * @return array{
	 *     is_gift: bool,
	 *     field_data: array,
	 * } Array of gift field data.
	 */
	public function get_order_gift_data( WC_Order $order ): array {
		$data = array(
			'is_gift'    => false, // Whether the order is marked as a gift.
			'field_data' => array(), // List of gift field data.
		);

		$is_gift_meta_key = self::get_block_prefixed_meta_key( 'is_gift' );

		foreach ( $this->get_gift_fields() as $gift_field ) {
			$meta_key        = self::get_block_prefixed_meta_key( $gift_field['id'] );
			$field_value     = $order->get_meta( $meta_key );
			$formatted_value = $field_value;

			// If this is not a checkbox field, skip if the field value is empty.
			if ( empty( $field_value ) && 'checkbox' !== $gift_field['type'] ) {
				continue;
			}

			if ( 'checkbox' === $gift_field['type'] ) {
				$is_checked      = ! empty( $field_value );
				$formatted_value = $is_checked ? __( 'Yes', 'woocommerce-shipstation-integration' ) : __( 'No', 'woocommerce-shipstation-integration' );

				// If the order is marked as a gift, set the checkbox value to true.
				if ( $is_checked && $is_gift_meta_key === $meta_key ) {
					$data['is_gift'] = true;
				}
			} elseif ( 'text' === $gift_field['type'] ) {
				// Truncate the value if it is too long for the gift message field.
				$formatted_value = wp_trim_words( $field_value, 13, '...' );
			}

			$data['field_data'][ self::get_namespaced_field_key( $gift_field['id'] ) ] = array(
				'id'      => $meta_key,
				'label'   => $gift_field['label'],
				'value'   => $formatted_value,
				'cbvalue' => true, // It's needed for the checkbox field.
				'type'    => $gift_field['type'],
			);
		}

		return $data;
	}

	/**
	 * Displays gift fields for the customer order detail views if conditions are met.
	 * Shows gift details only if:
	 * - Order was created via checkout OR using a block theme
	 * - Gift fields data exists
	 * - Order is marked as a gift
	 *
	 * @param WC_Order|int $order Order object or order ID.
	 *
	 * @return void
	 */
	public function maybe_display_order_gift_data_for_customers( $order ): void {
		$order = wc_get_order( $order );

		if ( ! $order instanceof WC_Order ) {
			return;
		}

		$created_via_checkout = 'checkout' === $order->get_created_via();
		if ( ! $created_via_checkout ) {
			return;
		}

		$gift_data       = $this->get_order_gift_data( $order );
		$is_gift         = $gift_data['is_gift'];
		$gift_field_data = $gift_data['field_data'];

		if ( ! $is_gift || empty( $gift_field_data ) ) {
			return;
		}

		$rendered_data = array_map(
			function ( $gift_data ) {
				return sprintf( '<dt>%1$s</dt><dd>%2$s</dd>', $gift_data['label'], $gift_data['value'] );
			},
			$gift_field_data
		);

		echo '<section class="wc-block-order-confirmation-additional-fields-wrapper wc-shipstation">';
		echo '<h2>' . esc_html__( 'Additional information', 'woocommerce-shipstation-integration' ) . '</h2>';
		echo '<dl class="wc-block-components-additional-fields-list">' . implode( '', $rendered_data ) . '</dl>'; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		echo '</section>';
	}

	/**
	 * Show or hide gift fields based on conditions.
	 *
	 * @param bool  $show_field Should field be shown flag.
	 * @param array $field       Field data.
	 *
	 * @return bool
	 */
	public function filter_fields_for_order_confirmation( $show_field, $field ): bool {
		if ( ! isset( $field['id'] ) ) {
			return $show_field;
		}

		if ( self::get_namespaced_field_key( 'is_gift' ) === $field['id'] ) {
			return isset( $field['value'] ) && 'Yes' === $field['value'] ? true : false;
		}

		if ( self::get_namespaced_field_key( 'gift_message' ) === $field['id'] ) {
			return ! empty( $field['value'] ) ? true : false;
		}

		return $show_field;
	}

	/**
	 * Displays or modifies gift data fields shown below shipping fields in the admin order screen.
	 * When context is 'edit', removes gift fields from shipping fields to prevent editing the fields.
	 * When context is not 'edit', adds gift data to shipping fields if the order is marked as gift.
	 *
	 * @param array          $shipping_fields List of shipping fields to display in admin.
	 * @param WC_Order|false $order           WooCommerce order object containing gift data.
	 * @param string         $context         Context of display - 'edit' for editing, other for viewing.
	 *
	 * @return array Modified list of shipping fields including gift data if applicable.
	 */
	public function maybe_display_gift_data_below_admin_shipping_fields( $shipping_fields, $order, $context ): array {

		// Hide fields in edit context, don't allow to edit values.
		if ( 'edit' === $context ) {
			foreach ( $this->get_gift_fields() as $gift_field ) {
				unset( $shipping_fields[ self::get_namespaced_field_key( $gift_field['id'] ) ] );
			}

			return $shipping_fields;
		}

		if ( ! $order instanceof WC_Order ) {
			return $shipping_fields;
		}

		$gift_data       = $this->get_order_gift_data( $order );
		$is_gift         = $gift_data['is_gift'];
		$gift_field_data = $gift_data['field_data'];

		if ( ! $is_gift || empty( $gift_field_data ) ) {
			return $shipping_fields;
		}

		return array_merge( $shipping_fields, $gift_field_data );
	}

	/**
	 * Set session.
	 *
	 * @param string $name  Session name.
	 * @param mixed  $value Session value.
	 *
	 * @return void
	 */
	public static function session_set( string $name, $value ): void {
		unset( WC()->session->$name );
		WC()->session->$name = $value;
	}

	/**
	 * Get Session.
	 *
	 * @param string $name Session name.
	 *
	 * @return mixed Session.
	 */
	public static function session_get( string $name ) {
		if ( isset( WC()->session->$name ) ) {
			return WC()->session->$name;
		}

		return null;
	}

	/**
	 * Delete session variables.
	 *
	 * @return void
	 */
	public function delete_sessions(): void {
		self::session_delete( WC_ShipStation_Integration::$order_meta_keys['is_gift'] );
		self::session_delete( WC_ShipStation_Integration::$order_meta_keys['gift_message'] );
	}

	/**
	 * Delete session.
	 *
	 * @param string $name Session name.
	 *
	 * @return void
	 */
	public static function session_delete( string $name ): void {
		unset( WC()->session->$name );
	}

	/**
	 * Get the fully prefixed block checkout meta key for a given meta key.
	 *
	 * @param string $meta_key The meta key.
	 *
	 * @return string The fully prefixed block checkout meta key.
	 */
	public static function get_block_prefixed_meta_key( string $meta_key ): string {
		return CheckoutFields::OTHER_FIELDS_PREFIX . self::get_namespaced_field_key( $meta_key );
	}

	/**
	 * Get the namespaced field key.
	 *
	 * @param string $field_key The field key to be namespaced.
	 *
	 * @return string The namespaced field key.
	 */
	public static function get_namespaced_field_key( string $field_key ): string {
		$field_key = WC_ShipStation_Integration::$order_meta_keys[ $field_key ] ?? $field_key;

		return self::FIELD_NAMESPACE . '/' . $field_key;
	}

	/**
	 * Get the CSS class field key.
	 *
	 * @param string $field_key The field key to be namespaced.
	 *
	 * @return string The CSS class.
	 */
	public static function get_css_class_field_key( string $field_key ): string {
		$field_key = WC_ShipStation_Integration::$order_meta_keys[ $field_key ] ?? $field_key;

		return self::FIELD_NAMESPACE . '-' . $field_key;
	}

	/**
	 * Check if the cart needs shipping in a safe way.
	 *
	 * @return bool
	 */
	private static function cart_needs_shipping(): bool {
		if ( ! WC()->cart instanceof WC_Cart || ! wc_shipping_enabled() ) {
			return false;
		}

		foreach ( WC()->cart->get_cart_contents() as $values ) {
			if ( ! isset( $values['data'] ) || ! method_exists( $values['data'], 'needs_shipping' ) ) {
				continue;
			}

			if ( $values['data']->needs_shipping() ) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Check if the page contains checkout.
	 *
	 * @return bool
	 */
	private static function is_checkout(): bool {
		return self::is_block_checkout() || self::is_classic_checkout();
	}

	/**
	 * Check if the page contains block checkout.
	 *
	 * @return bool
	 */
	private static function is_block_checkout(): bool {
		if (
			! function_exists( 'is_checkout' )
			|| ! function_exists( 'has_block' )
		) {
			return false;
		}
		return is_checkout() && has_block( 'woocommerce/checkout' );
	}

	/**
	 * Check if page contains classic checkout.
	 *
	 * @return bool
	 */
	private static function is_classic_checkout(): bool {
		if (
			! function_exists( 'is_checkout' )
			|| ! function_exists( 'wc_post_content_has_shortcode' )
			|| ! function_exists( 'has_block' )
		) {
			return false;
		}
		return is_checkout() && ( wc_post_content_has_shortcode( 'woocommerce_checkout' ) || has_block( 'woocommerce/classic-shortcode' ) );
	}

	/**
	 * AJAX handler for saving field value to session.
	 *
	 * @return void
	 */
	public function ajax_save_field_value(): void {
		check_ajax_referer( 'update-order-review', 'security' );

		if ( ! isset( $_POST['field_id'] ) || ! isset( $_POST['value'] ) ) {
			wp_send_json_error( 'Missing required parameters' );
		}

		$field_id = sanitize_text_field( wp_unslash( $_POST['field_id'] ) );

		// Only allow saving specific fields.
		$allowed_fields = array(
			WC_ShipStation_Integration::$order_meta_keys['is_gift'],
			WC_ShipStation_Integration::$order_meta_keys['gift_message'],
		);

		if ( ! in_array( $field_id, $allowed_fields, true ) ) {
			wp_send_json_error( 'Invalid field ID' );
		}

		$value = wp_unslash( $_POST['value'] ); // phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized --- Input is sanitized later.

		// Apply appropriate sanitization based on the field type.
		if ( WC_ShipStation_Integration::$order_meta_keys['is_gift'] === $field_id ) {
			$value = sanitize_text_field( $value );
		} elseif ( WC_ShipStation_Integration::$order_meta_keys['gift_message'] === $field_id ) {
			$value = sanitize_textarea_field( $value );
		}

		self::session_set( $field_id, $value );
		wp_send_json_success();
	}

	/**
	 * Sanitize the gift message field value.
	 *
	 * @param string $gift_message Gift message.
	 *
	 * @return string Sanitized gift message.
	 */
	private function sanitize_gift_message( string $gift_message ): string {
		return sanitize_textarea_field( wp_encode_emoji( $gift_message ) );
	}

	/**
	 * AJAX handler for loading field values from session.
	 *
	 * @return void
	 */
	public function ajax_load_field_values(): void {
		check_ajax_referer( 'update-order-review', 'security' );

		$is_gift_field_id      = WC_ShipStation_Integration::$order_meta_keys['is_gift'];
		$gift_message_field_id = WC_ShipStation_Integration::$order_meta_keys['gift_message'];

		$field_values = array(
			$is_gift_field_id      => self::session_get( $is_gift_field_id ),
			$gift_message_field_id => self::session_get( $gift_message_field_id ),
		);

		// Filter out null values.
		$field_values = array_filter(
			$field_values,
			function ( $value ) {
				return null !== $value;
			}
		);

		wp_send_json_success( $field_values );
	}

	/**
	 * Display gift fields in order confirmation email beneath customer details.
	 *
	 * @param WC_Order $order         Order object.
	 * @param bool     $sent_to_admin Whether the email is being sent to admin.
	 * @param bool     $plain_text    Whether the email is plain text.
	 *
	 * @return void
	 */
	public function display_gift_fields_in_order_email( WC_Order $order, bool $sent_to_admin = false, bool $plain_text = false ): void {
		if ( ! WC_ShipStation_Integration::$gift_enabled ) {
			return;
		}

		// Block Checkout (Store API) already includes gift field data in emails, so skip.
		if ( 'store-api' === $order->get_created_via() ) {
			return;
		}

		// Get gift data from the order.
		$gift_data = $this->get_order_gift_data( $order );

		// Only proceed if this is a gift order and we have gift field data.
		if ( ! $gift_data['is_gift'] || empty( $gift_data['field_data'] ) ) {
			return;
		}

		if ( $plain_text ) {
			echo "\n" . esc_html( wc_strtoupper( __( 'Additional information', 'woocommerce-shipstation-integration' ) ) ) . "\n\n";
			foreach ( $gift_data['field_data'] as $field ) {
				printf( "%s: %s\n", wp_kses_post( $field['label'] ), wp_kses_post( $field['value'] ) );
			}
		} else {
			echo '<h2>' . esc_html__( 'Additional information', 'woocommerce-shipstation-integration' ) . '</h2>';
			echo '<ul class="additional-fields" style="margin-bottom: 40px;">';
			foreach ( $gift_data['field_data'] as $field ) {
				printf( '<li><strong>%s</strong>: %s</li>', wp_kses_post( $field['label'] ), wp_kses_post( $field['value'] ) );
			}
			echo '</ul>';
		}
	}
}

new Checkout();
</file>

<file path="includes/class-wc-shipstation-api.php">
<?php
/**
 * Class WC_Shipstation_API file.
 *
 * @package WC_ShipStation
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

require_once WC_SHIPSTATION_ABSPATH . 'includes/api/requests/class-wc-shipstation-api-request.php';

/**
 * WC_Shipstation_API Class
 */
class WC_Shipstation_API extends WC_Shipstation_API_Request {

	/**
	 * Stores whether or not shipstation has been authenticated.
	 *
	 * @var boolean
	 */
	private static $authenticated = false;

	/**
	 * Being used to store $_GET variable from ShipStation API request.
	 *
	 * @var array
	 */
	protected $request;

	/**
	 * Constructor
	 */
	public function __construct() {
		nocache_headers();

		if ( ! defined( 'DONOTCACHEPAGE' ) ) {
			define( 'DONOTCACHEPAGE', 'true' );
		}

		if ( ! defined( 'DONOTCACHEOBJECT' ) ) {
			define( 'DONOTCACHEOBJECT', 'true' );
		}

		if ( ! defined( 'DONOTCACHEDB' ) ) {
			define( 'DONOTCACHEDB', 'true' );
		}

		self::$authenticated = false;

		$this->request();
	}

	/**
	 * Has API been authenticated?
	 *
	 * @return bool
	 */
	public static function authenticated() {
		return self::$authenticated;
	}

	/**
	 * Handle the request
	 */
	public function request() {
		// phpcs:disable WordPress.Security.NonceVerification.Recommended --- Using WC_ShipStation_Integration::$auth_key for security verification
		if ( empty( $_GET['auth_key'] ) ) {
			$this->trigger_error( esc_html__( 'Authentication key is required!', 'woocommerce-shipstation-integration' ) );
		}

		if ( ! hash_equals( sanitize_text_field( wp_unslash( $_GET['auth_key'] ) ), WC_ShipStation_Integration::$auth_key ) ) {
			$this->trigger_error( esc_html__( 'Invalid authentication key', 'woocommerce-shipstation-integration' ) );
		}

		$request = $_GET;

		if ( isset( $request['action'] ) ) {
			$this->request = array_map( 'sanitize_text_field', $request );
		} else {
			$this->trigger_error( esc_html__( 'Invalid request', 'woocommerce-shipstation-integration' ) );
		}

		self::$authenticated = true;

		if ( in_array( $this->request['action'], array( 'export', 'shipnotify' ), true ) ) {
			$mask = array(
				'auth_key' => '***',
			);

			$obfuscated_request = $mask + $this->request;

			/* translators: 1: query string */
			$this->log( sprintf( esc_html__( 'Input params: %s', 'woocommerce-shipstation-integration' ), http_build_query( $obfuscated_request ) ) );
			$request_class = include WC_SHIPSTATION_ABSPATH . 'includes/api/requests/class-wc-shipstation-api-' . $this->request['action'] . '.php';
			$request_class->request();
		} else {
			$this->trigger_error( esc_html__( 'Invalid request', 'woocommerce-shipstation-integration' ) );
		}

		exit;
		// phpcs:enable WordPress.Security.NonceVerification.Recommended
	}
}

new WC_Shipstation_API();
</file>

<file path="composer.json">
{
    "name": "woocommerce/woocommerce-shipstation",
    "description": "Ship your WooCommerce orders with confidence, save on top carriers, and automate your processes with ShipStation.",
    "homepage": "https://woocommerce.com/products/shipstation-integration/",
    "type": "wordpress-plugin",
    "license": "GPL-2.0+",
    "archive": {
        "exclude": [
            "!/assets",
            "node_modules",
            "bin",
            "tests",
            "/vendor",
            "Gruntfile.js",
            "README.md",
            "package.json",
            "package-lock.json",
            "composer.json",
            "composer.lock",
            "phpunit.xml.dist",
            "woocommerce-shipstation.zip",
            ".*",
            "pnpm-lock.yaml"
        ]
    },
    "require-dev": {
        "wp-coding-standards/wpcs": "*",
        "dealerdirect/phpcodesniffer-composer-installer": "*",
        "woocommerce/qit-cli": "*",
        "woocommerce/woocommerce-sniffs": "*",
        "phpstan/phpstan": "^2",
        "szepeviktor/phpstan-wordpress": "^2",
        "php-stubs/wp-cli-stubs": "*",
        "lucasbustamante/stubz": "^0"
    },
    "config": {
        "allow-plugins": {
            "dealerdirect/phpcodesniffer-composer-installer": true
        }
    },
    "scripts": {
        "phpstan": [
            "./vendor/bin/phpstan analyse --configuration=.phpstan/local-config.neon --level=2"
        ],
        "check-security": [
            "./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=./.phpcs.security.xml  --report-full --report-summary"
        ],
        "check-php": [
            "./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"
        ],
        "check-php:fix": [
            "./vendor/bin/phpcbf . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"
        ],
        "check-all": [
            "./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors -s"
        ],
        "check-all:fix": [
            "./vendor/bin/phpcbf . --ignore=vendor,.git,.phpstan,assets,node_modules,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"
        ],
        "qit:security": [
            "pnpm run build && composer install && ./vendor/bin/qit run:security woocommerce-shipstation-integration --zip=woocommerce-shipstation.zip"
        ],
        "qit:plugin-check": [
            "pnpm run build && composer install && ./vendor/bin/qit run:plugin-check woocommerce-shipstation-integration --zip=woocommerce-shipstation.zip"
        ]
    }
}
</file>

<file path="includes/class-wc-shipstation-integration.php">
<?php
/**
 * Class WC_ShipStation_Integration file.
 *
 * @package WC_ShipStation
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use WooCommerce\ShipStation\Order_Util;

/**
 * WC_ShipStation_Integration Class
 */
class WC_ShipStation_Integration extends WC_Integration {
	use Order_Util;

	/**
	 * Authorization key for ShipStation API.
	 *
	 * @var string
	 */
	public static $auth_key = null;

	/**
	 * Export statuses.
	 *
	 * @var array
	 */
	public static $export_statuses = array();

	/**
	 * Flag for logging feature.
	 * `true` means log feature is on.
	 *
	 * @var boolean
	 */
	public static $logging_enabled = true;

	/**
	 * Shipment status.
	 *
	 * @var string
	 */
	public static $shipped_status = null;

	/**
	 * Gift enable flag.
	 *
	 * @var boolean
	 */
	public static $gift_enabled = false;

	/**
	 * Order meta keys.
	 *
	 * @var array
	 */
	public static array $order_meta_keys = array(
		'is_gift'      => 'shipstation_is_gift',
		'gift_message' => 'shipstation_gift_message',
	);

	/**
	 * Constructor
	 */
	public function __construct() {
		$this->id                 = 'shipstation';
		$this->method_title       = __( 'ShipStation', 'woocommerce-shipstation-integration' );
		$this->method_description = __( 'ShipStation allows you to retrieve &amp; manage orders, then print labels &amp; packing slips with ease.', 'woocommerce-shipstation-integration' );

		if ( ! get_option( 'woocommerce_shipstation_auth_key', false ) ) {
			update_option( 'woocommerce_shipstation_auth_key', $this->generate_key() );
		}

		// Load admin form.
		$this->init_form_fields();

		// Load settings.
		$this->init_settings();

		self::$auth_key        = get_option( 'woocommerce_shipstation_auth_key', false );
		self::$export_statuses = $this->get_option( 'export_statuses', array( 'wc-processing', 'wc-on-hold', 'wc-completed', 'wc-cancelled' ) );
		self::$logging_enabled = 'yes' === $this->get_option( 'logging_enabled', 'yes' );
		self::$shipped_status  = $this->get_option( 'shipped_status', 'wc-completed' );
		self::$gift_enabled    = 'yes' === $this->get_option( 'gift_enabled', 'no' );

		// Force saved .
		$this->settings['auth_key'] = self::$auth_key;

		// Hooks.
		add_action( 'woocommerce_update_options_integration_shipstation', array( $this, 'update_shipstation_options' ) );
		add_filter( 'woocommerce_subscriptions_renewal_order_meta_query', array( $this, 'subscriptions_renewal_order_meta_query' ), 10, 4 );
		add_action( 'wp_loaded', array( $this, 'hide_notices' ) );
		add_filter( 'woocommerce_translations_updates_for_woocommerce_shipstation_integration', '__return_true' );

		$hide_notice               = get_option( 'wc_shipstation_hide_activate_notice', '' );
		$settings_notice_dismissed = get_user_meta( get_current_user_id(), 'dismissed_shipstation-setup_notice' );

		// phpcs:ignore WordPress.WP.Capabilities.Unknown --- It's native capability from WooCommerce
		if ( current_user_can( 'manage_woocommerce' ) && ( 'yes' !== $hide_notice && ! $settings_notice_dismissed ) ) {
			if ( ! isset( $_GET['wc-shipstation-hide-notice'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended --- No need to use nonce as no DB operation
				add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts' ) );
				add_action( 'admin_notices', array( $this, 'settings_notice' ) );
			}
		}

		add_filter( 'woocommerce_order_query_args', array( $this, 'add_order_number_query_vars_for_hpos' ), 10, 1 );
		add_filter( 'woocommerce_order_data_store_cpt_get_orders_query', array( $this, 'add_order_number_query_vars_for_cpt' ), 10, 2 );
	}

	/**
	 * Update options for ShipStation settings.
	 * This method is needed for `woocommerce_update_options_integration_shipstation` action hook.
	 * `WC_Integration::process_admin_options()` cannot be used directly to that action hook as it return value and PHPStan won't allow it.
	 */
	public function update_shipstation_options() {
		$this->process_admin_options();
	}

	/**
	 * Handle a custom variable query var to get orders with the 'order_number' meta for HPOS.
	 *
	 * @param array $query_vars - Query vars from WC_Order_Query.
	 *
	 * @return array modified $query_vars
	 */
	public function add_order_number_query_vars_for_hpos( $query_vars ) {
		if ( ! self::custom_orders_table_usage_is_enabled() ) {
			return $query_vars;
		}

		if ( ! empty( $query_vars['wt_order_number'] ) ) {
			$query_vars['meta_query'][] = array(
				'key'   => '_order_number',
				'value' => esc_attr( $query_vars['wt_order_number'] ),
			);
		}

		return $query_vars;
	}

	/**
	 * Handle a custom variable query var to get orders with the 'order_number' meta for order post type.
	 *
	 * @param array $query      Main query of WC_Order_Query.
	 * @param array $query_vars Query vars from WC_Order_Query.
	 *
	 * @return array modified $query.
	 */
	public function add_order_number_query_vars_for_cpt( $query, $query_vars ) {
		if ( ! empty( $query_vars['wt_order_number'] ) ) {
			$query['meta_query'][] = array(
				'key'   => '_order_number',
				'value' => esc_attr( $query_vars['wt_order_number'] ),
			);
		}

		return $query;
	}

	/**
	 * Enqueue admin scripts/styles
	 */
	public function enqueue_scripts() {
		wp_enqueue_style( 'shipstation-admin', plugins_url( 'assets/css/admin.css', WC_SHIPSTATION_FILE ), array(), WC_SHIPSTATION_VERSION );
	}

	/**
	 * Generate a key.
	 *
	 * @return string
	 */
	public function generate_key() {
		$to_hash = get_current_user_id() . wp_date( 'U' ) . wp_rand();
		return 'WCSS-' . hash_hmac( 'md5', $to_hash, wp_hash( $to_hash ) );
	}

	/**
	 * Init integration form fields
	 */
	public function init_form_fields() {
		$this->form_fields = include WC_SHIPSTATION_ABSPATH . 'includes/data/data-settings.php';

		// If Checkout class does not exist, disable the gift option.
		if ( ! class_exists( 'WooCommerce\Shipping\ShipStation\Checkout' ) ) {
			$this->form_fields['gift_enabled']['custom_attributes'] = array( 'disabled' => 'disabled' );
			$this->form_fields['gift_enabled']['description']       = __( 'This feature requires WooCommerce 9.7.0 or higher.', 'woocommerce-shipstation-integration' );
		}
	}

	/**
	 * Prevents WooCommerce Subscriptions from copying across certain meta keys to renewal orders.
	 *
	 * @param string $order_meta_query Order meta query.
	 * @param int    $original_order_id Original order ID.
	 * @param int    $renewal_order_id Order ID after being renewed.
	 * @param string $new_order_role New order role.
	 *
	 * @return array
	 */
	public function subscriptions_renewal_order_meta_query( $order_meta_query, $original_order_id, $renewal_order_id, $new_order_role ) {
		if ( 'parent' === $new_order_role ) {
			$order_meta_query .= ' AND `meta_key` NOT IN ('
							. "'_tracking_provider', "
							. "'_tracking_number', "
							. "'_date_shipped', "
							. "'_order_custtrackurl', "
							. "'_order_custcompname', "
							. "'_order_trackno', "
							. "'_order_trackurl' )";
		}
		return $order_meta_query;
	}

	/**
	 * Hides any admin notices.
	 *
	 * @since 4.1.37
	 * @return void
	 */
	public function hide_notices() {
		if ( isset( $_GET['wc-shipstation-hide-notice'] ) && isset( $_GET['_wc_shipstation_notice_nonce'] ) ) {
			if ( ! wp_verify_nonce( sanitize_key( $_GET['_wc_shipstation_notice_nonce'] ), 'wc_shipstation_hide_notices_nonce' ) ) { //phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized, nonce is unslashed and verified.
				wp_die( esc_html__( 'Action failed. Please refresh the page and retry.', 'woocommerce-shipstation-integration' ) );
			}

			// phpcs:ignore WordPress.WP.Capabilities.Unknown --- It's native capability from WooCommerce
			if ( ! current_user_can( 'manage_woocommerce' ) ) {
				wp_die( esc_html__( 'Cheatin&#8217; huh?', 'woocommerce-shipstation-integration' ) );
			}

			update_option( 'wc_shipstation_hide_activate_notice', 'yes' );
		}
	}

	/**
	 * Settings prompt
	 */
	public function settings_notice() {
		// phpcs:ignore WordPress.Security.NonceVerification.Recommended --- No need to use nonce as no DB operation
		if ( ! empty( $_GET['tab'] ) && 'integration' === $_GET['tab'] ) {
			return;
		}

		$logo_title = __( 'ShipStation logo', 'woocommerce-shipstation-integration' );
		?>
		<div class="notice notice-warning">
			<img class="shipstation-logo" alt="<?php echo esc_attr( $logo_title ); ?>" title="<?php echo esc_attr( $logo_title ); ?>" src="<?php echo esc_url( plugins_url( 'assets/images/shipstation-logo.svg', __DIR__ ) ); ?>" />
			<a class="woocommerce-message-close notice-dismiss woocommerce-shipstation-activation-notice-dismiss" href="<?php echo esc_url( wp_nonce_url( add_query_arg( 'wc-shipstation-hide-notice', '' ), 'wc_shipstation_hide_notices_nonce', '_wc_shipstation_notice_nonce' ) ); ?>"></a>
			<p>
				<?php
				printf(
					wp_kses(
						/* translators: %s: ShipStation URL */
						__( 'To begin printing shipping labels with ShipStation head over to <a class="shipstation-external-link" href="%s" target="_blank">ShipStation.com</a> and log in or create a new account.', 'woocommerce-shipstation-integration' ),
						array(
							'a' => array(
								'class'  => array(),
								'href'   => array(),
								'target' => array(),
							),
						)
					),
					'https://www.shipstation.com/partners/woocommerce/?ref=partner-woocommerce'
				);
				?>
			</p>
			<p>
				<?php
					echo wp_kses(
						sprintf(
							/* translators: %s: ShipStation Auth Key */
							__( 'After logging in, add a selling channel for WooCommerce and use your Auth Key (<code>%s</code>) to connect your store.', 'woocommerce-shipstation-integration' ),
							self::$auth_key
						),
						array( 'code' => array() )
					);
				?>
			</p>
			<p><?php esc_html_e( "Once connected you're good to go!", 'woocommerce-shipstation-integration' ); ?></p>
			<hr>
			<p>
				<?php
				printf(
					wp_kses(
						/* translators: %1$s: ShipStation plugin settings URL, %2$s: ShipStation documentation URL */
						__( 'You can find other settings for this extension <a href="%1$s">here</a> and view the documentation <a href="%2$s">here</a>.', 'woocommerce-shipstation-integration' ),
						array(
							'a' => array(
								'href' => array(),
							),
						)
					),
					esc_url( admin_url( 'admin.php?page=wc-settings&tab=integration&section=shipstation' ) ),
					'https://docs.woocommerce.com/document/shipstation-for-woocommerce/'
				);
				?>
			</p>
		</div>
		<?php
	}
}
</file>

<file path="package.json">
{
	"name": "woocommerce-shipstation",
	"description": "Ship your WooCommerce orders with confidence, save on top carriers, and automate your processes with ShipStation.",
	"version": "4.7.6",
	"title": "ShipStation for WooCommerce",
	"devDependencies": {
		"clean-css-cli": "^4.2.1",
		"node-wp-i18n": "~1.2.3",
		"npm-run-all": "^4.1.5",
		"sass": "^1.77.5",
		"uglify-js": "^3.4.9"
	},
	"scripts": {
		"prebuild": "rm -rf ./vendor",
		"build": "pnpm run build:prod && pnpm run archive",
		"build:dev": "pnpm run uglify && pnpm run makepot && pnpm run sass",
		"build:prod": "pnpm run uglify && pnpm run makepot && pnpm run sass",
		"archive": "composer archive --file=$npm_package_name --format=zip",
		"postarchive": "rm -rf $npm_package_name && unzip $npm_package_name.zip -d $npm_package_name && rm $npm_package_name.zip && zip -r $npm_package_name.zip $npm_package_name && rm -rf $npm_package_name",
		"preuglify": "rm -f $npm_package_config_paths_js_min",
		"uglify": "sh -c 'for f in $npm_package_config_paths_js; do if [ -s \"$f\" ]; then echo Processing \"$f\"; file=${f%.js}; node_modules/.bin/uglifyjs \"$f\" -c -m > \"$file.min.js\" || echo \"Uglify failed for $f\"; else echo \"Skipping. No js file to process.\"; fi; done'",
		"presass": "rm -f $npm_package_config_paths_css",
		"sass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --no-source-map --style compressed",
		"watchsass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --watch",
		"postsass": "for f in $npm_package_config_paths_css; do echo Processing $f; file=${f%.css}; node_modules/.bin/cleancss -o $file.css $f; done",
		"makepot": "wpi18n makepot --domain-path languages --pot-file $npm_package_name.pot --type plugin --main-file $npm_package_name.php --exclude node_modules,tests,docs",
		"build:phpstan": "composer install --no-dev -o && pnpm run makepot && pnpm run archive:phpstan && pnpm run postarchive",
		"archive:phpstan": "composer archive --file=$npm_package_name --format=zip && pnpm run zip:phpstan_config",
		"zip:phpstan_config": "zip -r $npm_package_name.zip .phpstan/dist/* -j",
		"build:qit": "pnpm run build:prod && pnpm run archive:qit",
		"archive:qit": "composer archive --file=$npm_package_config_wp_org_slug --format=zip && zip -r $npm_package_config_wp_org_slug.zip .phpstan/dist/* -j",
		"postarchive:qit": "rm -rf $npm_package_config_wp_org_slug && unzip $npm_package_config_wp_org_slug.zip -d $npm_package_config_wp_org_slug && rm $npm_package_config_wp_org_slug.zip && zip -r $npm_package_config_wp_org_slug.zip $npm_package_config_wp_org_slug && rm -rf $npm_package_config_wp_org_slug"
	},
	"engines": {
		"node": "^22.14.0",
		"pnpm": "^10.4.1"
	},
	"config": {
		"use_pnpm": true,
		"wp_org_slug": "woocommerce-shipstation-integration",
		"use_gh_release_notes": true,
		"paths": {
			"js": "assets/js/*.js",
			"js_min": "assets/js/*.min.js",
			"css": "assets/css/*.css",
			"sass": "assets/sass",
			"cssfolder": "assets/css"
		}
	}
}
</file>

<file path="woocommerce-shipstation.php">
<?php
/**
 * Plugin Name: ShipStation for WooCommerce
 * Plugin URI: https://woocommerce.com/products/shipstation-integration/
 * Version: 4.7.6
 * Description: Ship your WooCommerce orders with confidence, save on top carriers, and automate your processes with ShipStation.
 * Author: WooCommerce
 * Author URI: https://woocommerce.com/
 * Text Domain: woocommerce-shipstation-integration
 * Domain Path: /languages
 * Requires Plugins: woocommerce
 * Requires PHP: 7.4
 * Requires at least: 6.7
 * Tested up to: 6.8
 * WC requires at least: 9.9
 * WC tested up to: 10.1
 *
 * Copyright: © 2025 WooCommerce
 * License: GPLv3
 * License URI: https://www.gnu.org/licenses/gpl-3.0.html
 *
 * @package WC_ShipStation
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

use WooCommerce\Shipping\ShipStation\REST_API_Loader;

define( 'WC_SHIPSTATION_FILE', __FILE__ );
define( 'WC_SHIPSTATION_ABSPATH', trailingslashit( __DIR__ ) );

if ( ! defined( 'WC_SHIPSTATION_PLUGIN_DIR' ) ) {
	define( 'WC_SHIPSTATION_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
}

if ( ! defined( 'WC_SHIPSTATION_PLUGIN_URL' ) ) {
	define( 'WC_SHIPSTATION_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
}

/**
 * WooCommerce fallback notice.
 *
 * @since 4.1.26
 *
 * @return void
 */
function woocommerce_shipstation_missing_wc_notice() {
	/* translators: %s WC download URL link. */
	echo '<div class="error"><p><strong>' . sprintf( esc_html__( 'Shipstation requires WooCommerce to be installed and active. You can download %s here.', 'woocommerce-shipstation-integration' ), '<a href="https://woocommerce.com/" target="_blank">WooCommerce</a>' ) . '</strong></p></div>';
}

/**
 * Include shipstation class.
 *
 * @since 1.0.0
 */
function woocommerce_shipstation_init() {
	if ( ! class_exists( 'WooCommerce' ) ) {
		add_action( 'admin_notices', 'woocommerce_shipstation_missing_wc_notice' );

		return;
	}

	define( 'WC_SHIPSTATION_VERSION', '4.7.6' ); // WRCS: DEFINED_VERSION.

	if ( ! defined( 'WC_SHIPSTATION_EXPORT_LIMIT' ) ) {
		define( 'WC_SHIPSTATION_EXPORT_LIMIT', 100 );
	}

	woocommerce_shipstation_includes();

	add_action( 'before_woocommerce_init', 'woocommerce_shipstation_before_woocommerce_init' );
	add_action( 'woocommerce_init', 'woocommerce_shipstation_load_rest_api' );
}

add_action( 'plugins_loaded', 'woocommerce_shipstation_init' );

/**
 * Run instances on time.
 *
 * @since 4.4.6
 */
function woocommerce_shipstation_before_woocommerce_init() {
	new WC_ShipStation_Privacy();
}

/**
 * Include needed files.
 *
 * @since 4.4.5
 */
function woocommerce_shipstation_includes() {
	// Include order util trait class file.
	require_once WC_SHIPSTATION_ABSPATH . 'includes/trait-woocommerce-order-util.php';
	include_once WC_SHIPSTATION_ABSPATH . 'includes/class-wc-shipstation-integration.php';
	include_once WC_SHIPSTATION_ABSPATH . 'includes/class-wc-shipstation-privacy.php';

	// Include the Checkout class if WooCommerce version is 9.7.0 or higher.
	// This class is used to handle the gift message feature in the checkout process.
	if ( version_compare( WC()->version, '9.7.0', '>=' ) ) {
		include_once WC_SHIPSTATION_ABSPATH . 'includes/class-checkout.php';
	}
}

/**
 * Initialize REST API.
 *
 * @since 4.5.2
 */
function woocommerce_shipstation_load_rest_api() {
	// Load REST API loader class file.
	require_once WC_SHIPSTATION_ABSPATH . 'includes/class-rest-api-loader.php';

	// Initialize REST API.
	$rest_loader = new REST_API_Loader();
	$rest_loader->init();
}


/**
 * Define integration.
 *
 * @since 1.0.0
 *
 * @param array $integrations Integrations.
 *
 * @return array Integrations.
 */
function woocommerce_shipstation_load_integration( $integrations ) {
	$integrations[] = 'WC_ShipStation_Integration';

	return $integrations;
}

add_filter( 'woocommerce_integrations', 'woocommerce_shipstation_load_integration' );

/**
 * Listen for API requests.
 *
 * @since 1.0.0
 */
function woocommerce_shipstation_api() {
	include_once WC_SHIPSTATION_ABSPATH . 'includes/class-wc-shipstation-api.php';
}

add_action( 'woocommerce_api_wc_shipstation', 'woocommerce_shipstation_api' );

/**
 * Added ShipStation custom plugin action links.
 *
 * @since 4.1.17
 * @version 4.1.17
 *
 * @param array $links Links.
 *
 * @return array Links.
 */
function woocommerce_shipstation_api_plugin_action_links( $links ) {
	$setting_link = admin_url( 'admin.php?page=wc-settings&tab=integration&section=shipstation' );
	$plugin_links = array(
		'<a href="' . $setting_link . '">' . __( 'Settings', 'woocommerce-shipstation-integration' ) . '</a>',
		'<a href="https://woocommerce.com/my-account/tickets">' . __( 'Support', 'woocommerce-shipstation-integration' ) . '</a>',
		'<a href="https://docs.woocommerce.com/document/shipstation-for-woocommerce/">' . __( 'Docs', 'woocommerce-shipstation-integration' ) . '</a>',
	);

	return array_merge( $plugin_links, $links );
}
add_filter( 'plugin_action_links_' . plugin_basename( WC_SHIPSTATION_FILE ), 'woocommerce_shipstation_api_plugin_action_links' );

/**
 * Declaring HPOS compatibility.
 */
function woocommerce_shipstation_declare_hpos_compatibility() {
	if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
		\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', 'woocommerce-shipstation/woocommerce-shipstation.php', true );
	}
}
add_action( 'before_woocommerce_init', 'woocommerce_shipstation_declare_hpos_compatibility' );
</file>

<file path="changelog.txt">
*** ShipStation for WooCommerce ***

= 4.7.6 - 2025-08-11 =
* Tweak - WooCommerce 10.1 compatibility.

= 4.7.5 - 2025-08-05 =
* Fix   - Out of memory allocation error on checkout page.
* Fix   - Remove deprecated load_plugin_textdomain() call.

= 4.7.4 - 2025-07-07 =
* Tweak - WooCommerce 10.0 compatibility.

= 4.7.3 - 2025-06-30 =
* Fix   - Compatibility issue with WooCommerce version lower than 8.9.

= 4.7.2 - 2025-06-24 =
* Fix   - Fatal error on Checkout page.

= 4.7.1 - 2025-06-18 =
* Fix   - Fatal error on WooCommerce Subscriptions edit page.

= 4.7.0 - 2025-06-17 =
* Add   - REST API endpoints to update and retrieve product inventory data.
* Add   - Gift feature.

= 4.6.1 - 2025-06-09 =
* Tweak - WooCommerce 9.9 compatibility.

= 4.6.0 - 2025-06-02 =
* Add   - New hook `woocommerce_shipstation_shipnotify_status_updated` that will be called after the order status is changed.
* Add   - REST API endpoints to update and retrieve product inventory data.

= 4.5.2 - 2025-05-26 =
* Fix   - Security updates.
* Tweak - Update ShipStation branding.

= 4.5.1 - 2025-04-22 =
* Add   - Include the product dimensions when exporting an order to ShipStation.
* Tweak - Added a filter to allow the user to disable exporting order discounts as a separate line item to ShipStation.

= 4.5.0 - 2025-04-14 =
* Add   - woocommerce_shipstation_shipnotify_order_shipped filter - Allow to override is order shipped.
* Add   - woocommerce_shipstation_shipnotify_tracking_note filter - Allow to override tracking note.
* Add   - woocommerce_shipstation_shipnotify_send_tracking_note filter - Allow to override should tracking note be sent to customer.
* Tweak - Move woocommerce_shipstation_shipnotify action before order status is updated.

= 4.4.9 - 2025-04-07 =
* Tweak - WooCommerce 9.8 compatibility.

= 4.4.8 - 2025-03-10 =
* Fix   - Make the value of `woocommerce_shipstation_get_order_id` filter consistent by removing the conversion function.

= 4.4.7 - 2025-03-04 =
* Tweak - PHP 8.4 Compatibility.
* Tweak - WooCommerce 9.7 Compatibility.

= 4.4.6 - 2024-11-27 =
* Tweak - Reimplemented compatibility with WordPress 6.7 while maintaining unchanged execution priorities.

= 4.4.5 - 2024-10-28 =
* Tweak - WordPress 6.7 Compatibility.

= 4.4.4 - 2024-07-02 =
* Fix   - Security updates.
* Tweak - WooCommerce 9.0 and WordPress 6.6 Compatibility.

= 4.4.3 - 2024-05-27 =
* Tweak - Performance enhancement.

= 4.4.2 - 2024-04-09 =
* Fix - Cannot retrieve order number on from GET variable.

= 4.4.1 - 2024-03-25 =
* Tweak - WordPress 6.5 compatibility.

= 4.4.0 - 2024-03-19 =
* Fix - Applying WordPress coding standards.

= 4.3.9 - 2023-09-05 =
* Fix - Security updates.
* Tweaks - Developer dependencies update.
* Add - Developer QIT workflow.

= 4.3.8 - 2023-08-09 =
* Fix - Security updates.

= 4.3.7 - 2023-05-08 =
* Fix - Allow filtering the order exchange rate and currency code before exporting to ShipStation.

= 4.3.6 - 2023-04-20 =
* Fix - Compatibility for Sequential Order Numbers by WebToffee.
* Add - New query var for WC_Order_Query called `wt_order_number` to search order number.

= 4.3.5 - 2023-04-17 =
* Fix - Revert version 4.3.4's compatibility update for Sequential Order Numbers by WebToffee.

= 4.3.4 - 2023-04-12 =
* Fix - Compatibility for Sequential Order Numbers by WebToffee.

= 4.3.3 - 2023-03-29 =
* Fix - Fatal error when product image does not exist.

= 4.3.2 - 2022-11-29 =
* Fix - Use product variation name when exporting a product variation.

= 4.3.1 - 2022-10-25 =
* Add - Declared HPOS compatibility.

= 4.3.0 - 2022-10-13 =
* Add - High-Performance Order Storage compatibility.

= 4.2.0 - 2022-09-07 =
* Add   - Filter for manipulating address export data.
* Fix   - Remove unnecessary files from plugin zip file.
* Tweak - Transition version numbering to WordPress versioning.
* Tweak - WC 6.8 and WP 6.0 compatibility.
* Fix - Remove 'translate : true' in package.json.

= 4.1.48 - 2021-11-03 =
* Fix - Critical Error when null value is passed to appendChild method.
* Fix - $logging_enabled compared against string instead of boolean.

= 4.1.47 - 2021-09-29 =
* Fix - Change API Export order search to be accurate down to the second, not just the date.

= 4.1.46 - 2021-09-10 =
* Fix   - Order is not changed to completed when the order has partial refund and is marked as shipped in ShipStation.

= 4.1.45 - 2021-08-24 =
* Fix   - Remove all usage of deprecated $HTTP_RAW_POST_DATA.

= 4.1.44 - 2021-08-12 =
* Fix   - Changing text domain to "woocommerce-shipstation-integration" to match with plugin slug.
* Fix   - Order product quantities do not sync to Shipstation when using a refund.
* Fix   - PHP notice error "wc_cog_order_total_cost" was called incorrectly.

= 4.1.43 - 2021-07-27 =
* Fix   - API returns status code 200 even when errors exist.
* Tweak - Add version compare for deprecated Order::get_product_from_item().

= 4.1.42 - 2021-04-20 =
* Fix - Use order currency code instead of store currency.

= 4.1.41 - 2021-03-02 =
* Add - Add currency code and weight units to orders XML.

= 4.1.40 - 2020-11-24 =
* Tweak - PHP 8 compatibility fixes.

= 4.1.39 - 2020-10-06 =
* Add   - Add woocommerce_shipstation_export_order_xml filter.
* Tweak - Update Readme.
* Tweak - WC 4.5 compatibility.
* Fix   - Updated shop_thumbnail to woocommerce_gallery_thumbnail for thumbnail export.

= 4.1.38 - 2020-08-19 =
* Tweak - WordPress 5.5 compatibility.

= 4.1.37 - 2020-06-05 =
* Tweak - Dismiss activation notice independent of user.

= 4.1.36 - 2020-04-29 =
* Tweak - WC 4.1 compatibility.

= 4.1.35 - 2020-04-21 =
* Tweak - Obfuscate logging data.

= 4.1.34 2020-03-09 =
* Tweak - WP tested up to 5.4.
* Tweak - WC tested up to 4.0.

= 4.1.33 2020-03-04 =
* Tweak - Use code sniff version.
* Tweak - WC 4.0 compatibility.

= 4.1.32 2020-02-12 =
* Fix - Export shipping address even when shipping country is not available.

= 4.1.31 2020-01-15 =
* Tweak - WP 5.3 compatibility.
* Add   - Filter `woocommerce_shipstation_no_shipping_item` for when an item does not need shipping or is a fee.

= 4.1.30 2019-11-04 =
* Tweak - WC 3.8 compatibility.

= 4.1.29 2019-08-12 =
* Tweak - WC 3.7 compatibility.

= 4.1.28 2019-04-17 =
* Tweak - WC 3.6 compatibility.

= 4.1.27 2019-01-07 =
* Fix - Use product name from order instead of product itself.
* Fix - Prevent errors when WooCommerce isn't active.

= 4.1.26 2018-12-10 =
* Update - Setup notice link to WooCommerce-specific landing page.

= 4.1.25 2018-11-08 =
* Update - WP 5.0 compatibility.

= 4.1.24 2018-10-18 =
* Fix - Add missing language .pot file.
* Update - WC 3.5 compatibility.

= 4.1.23 2018-09-12 =
* Fix    - Use correct textdomain on some strings.
* Tweak  - Rework settings notice to correctly provide setup instructions.
* Tweak  - Coding standards and making the plugin ready for wordpress.org.

= 4.1.22 2018-05-24 =
* Fix    - Order timestamp issue.

= 4.1.21 2018-05-23 =
* Fix    - Privacy policy updates.

= 4.1.20 2018-05-23 =
* Fix    - Paid date not showing actual payment date, but Order Date instead.
* Update - Privacy policy notification.
* Update - Export/erasure hooks added.
* Update - WC 3.4 compatibility.

= 4.1.19 2017-12-15 =
* Fix - WC 3.3 compatibility.

= 4.1.18 2017-07-18 =
* Fix - Update the order status to complete if XML from ShipStation is not present in request's body. Also log the request information.
* Fix - Adjusted text domain for two strings so that they are now translateable.

= 4.1.17 2017-07-06 =
* Fix - Issue when a server couldn't read ShipNotify's XML posted in request's body, nothing is updated in the order.
* Tweak - Added setting, docs, and support links in plugin action links.

= 4.1.16 2017-06-14 =
* Fix - Issue where legacy code for converting sequential order numbers still used.
* Fix - Make sure to not count non shippable item when get notified from ShipStation.

= 4.1.15 2017-05-12 =
* Fix - Ensure some orders from previous version of ShipStation are able to be found on notifications.

= 4.1.14 2017-05-11 =
* Fix - Possible error when order is not found during shipment notification.
* Tweak - Order numbers are now sent via own XML field and will not display in invoice.

= 4.1.13 2017-05-05 =
* Fix - WC30 date/time not displaying correctly.
* Fix - Tax amount discrenpancy when sent to Shipstation.
* Fix - When using split orders, order does not get updated in WooCommerce.
* Tweak - Sequential Numbers Pro compatibility.
* Add - Exported order note when the order has been exported.

= 4.1.12 2017-05-02 =
* Fix - Product attributes not passing to Shipstation under certain conditions.

= 4.1.11 2017-05-01 =
* Fix - Export error due to WC30 incompatibility.

= 4.1.10 2017-04-10 =
* Fix  - Allow additional characters to be used for shipping service name

= 4.1.9 2017-04-06 =
* Fix  - Additional updates for WC 3.0 compatibility

= 4.1.8 2017-04-03 =
* Fix  - PHP 7 compatibility
* Fix  - Update for WC 3.0 compatibility

= 4.1.7 2016-10-03 =
* Fix  - Digital products are also sent through.
* Fix  - Checkout add on fee not being sent through.

= 4.1.6 2016-08-15 =
* Tweak - Added filter for ShipNotify order ID
* Tweak - Send payment method ShipStation
* Fix   - Issue where fee items not be exported to ShipStation

= 4.1.5 2016-02-24 =
* Fix   - Compatibility issue with WC Order Status Manager

= 4.1.4 2016-01-25 =
* Fix   - Compatibility issue with woocommerce-sequential-order-numbers-pro version 1-9-0

= 4.1.3 2015-09-23 =
* Fix   - Allow copy/paste from API key field in firefox

= 4.1.2 2015-08-21 =
* Fix   - Send pre-discount unit price.

= 4.1.1 2015-08-06 =
* Fix   - Send UnitPrice as single product total-
* Tweak - Date parsing.

= 4.1.0 2015-06-24 =
* Fix   - Sanitize XML response.
* Fix   - Prevent API requests being callable when not authenticated.
* Fix   - Prevent caching.
* Tweak - Use hash_equals to compare keys.
* Tweak - Send total discount to ShipStation.

= 4.0.9 2015-05-12 =
* Tweak - woocommerce_shipstation_export_order filter.
* Tweak - Exclude system notes.
* Tweak - Custom field value filters.

= 4.0.8 2015-04-03 =
* Fix   - Don't automatically set to $is_customer_note to true

= 4.0.7 2015-03-12 =
* Check if $product exists before checking if needs_shipping in export.

= 4.0.6 2015-01-16 =
* Send negative discount.

= 4.0.5 2015-01-08 =
* Export query based on post_modified_gmt rather than post_date_gmt

= 4.0.4 2014-11-19 =
* Fix compatibility with Sequential order numbers.

= 4.0.3 2014-11-13 =
* Extra logging in ShipNotify.
* Fixed completing orders with multiple lines.

= 4.0.2 2014-11-13 =
* Order results by date.
* Enforce minimum page 1.
* Removed check to see if orders need shipping to prevent issues with offset/max pages. Exports all orders.

= 4.0.1 2014-11-12 =
* Added 'pages' node to XML feed so ShipStation knows how many pages of results are present.

= 4.0.0 2014-11-01 =
* Completely refactored by WooThemes!
* Supports split orders (only completes the order once all items are shipped).
* Exports orders (from statuses you define).
* Excludes orders and items which do not require shipping.
* Simplified setup process; just requires an auth key.
* Exports order-level discounts as line items.
</file>

<file path="readme.txt">
=== ShipStation for WooCommerce ===
Contributors: woocommerce, automattic, royho, akeda, mattyza, bor0, woothemes, dwainm, laurendavissmith001, Kloon
Tags: shipping, woocommerce, automattic
Requires at least: 6.7
Tested up to: 6.8
WC tested up to: 10.1
WC requires at least: 9.9
Requires PHP: 7.4
Requires Plugins: woocommerce
Stable tag: 4.7.6
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.html

Ship your WooCommerce orders with confidence, save on top carriers, and automate your processes with ShipStation.

== Description ==

Ship your WooCommerce orders with confidence, save on top carriers, and automate your processes with ShipStation.

https://www.youtube.com/watch?v=p7603LVyp9M&t=100s

= Features =
- __Save money;__ get up to 84% off with top carriers, including UPS, USPS, and DHL Express.
- __Save time;__ sync orders from all your sales channels in one place, and use automation to speed up your processes.
- __Delight customers;__ deliver an exceptional experience with tracking, custom emails and SMS, plus a branded returns portal.
- __Expand across borders;__ automatically generate customs forms, verify addresses, and get low rates on international shipments.

= Get started =
This extension requires a ShipStation monthly plan — [sign up for a free 30-day trial](https://www.shipstation.com/partners/woocommerce/?ref=partner-woocommerce&utm_campaign=partner-referrals&utm_source=woocommerce&utm_medium=partner).

= Save money =
Save __up to 84%__ with UPS, USPS, and DHL Express. You'll also get seriously discounted rates from leading carriers in the US, Canada, UK, Australia, New Zealand, France, and Germany.

= Save time =
Connect your store in seconds, automate workflows, sync tracking info, and get products to your customers __fast__. Sync orders from all your sales channels (including Amazon, Walmart, eBay, and Etsy) in one place.

Get back hours of your time by automating, tagging, splitting, and batching orders and labels. Score!

= Delight your customers =
Deliver an exceptional experience __every time__ with customizable emails, SMS, and branded tracking info to keep customers updated. Returns? No problem, thanks to your own branded returns portal — now that's seamless.

= Expand your business across borders =
Global fulfillment just became effortless. With ShipStation, you can automatically generate and send __customs declarations__ and __verify overseas addresses__ in no time. Shipping to Canada from the US? International parcels are fast and affordable, with low, flat rate Canada Delivered Duty Paid (DDP).

= Why ShipStation? =
ShipStation powers global shipping success for businesses of all sizes. It streamlines the online fulfillment process — from order import and batch label creation to customer communication — with advanced customization features.

== Frequently Asked Questions ==

= Where can I find documentation and a setup guide? =
You’ve come to the right place. [Our documentation](https://woocommerce.com/document/shipstation-for-woocommerce/) for ShipStation for WooCommerce includes detailed setup instructions, troubleshooting tips, and more.

= Where can I get support? =
To start, [review our troubleshooting tips](https://woocommerce.com/document/shipstation-for-woocommerce/#troubleshooting) for answers to common questions. Then, if you need further assistance, get in touch via the [official support forum](https://wordpress.org/support/plugin/woocommerce-shipstation-integration/).

= Do I need a ShipStation account? =
Yes; [sign up for a free 30-day trial](https://www.shipstation.com/partners/woocommerce/?ref=partner-woocommerce&utm_campaign=partner-referrals&utm_source=woocommerce&utm_medium=partner).

= Does this extension provide real-time shipping quotes at checkout? =
No. Merchants will need a _real-time shipping quote extension_ (such as USPS, FedEx, UPS, etc.) or an alternate method (e.g. [flat rate charges](https://woocommerce.com/document/flat-rate-shipping/).

= Does ShipStation send data when not in use (e.g. for free shipping)? =
Yes; conditional exporting is not currently available.

= Why are multiple line items in a WooCommerce order combined when they reach ShipStation? =
This commonly occurs when products and variations do not have a unique [stock-keeping unit (SKU)](https://woocommerce.com/document/managing-products/product-editor-settings/#what-is-sku) assigned to them. Allocate a unique SKU to each product — and each variation of that product — to ensure order line items show up correctly in ShipStation.

= My question is not listed; where can I find more answers? =
[Review our general FAQ](https://woocommerce.com/document/shipstation-for-woocommerce/#frequently-asked-questions) or [contact support](https://wordpress.org/support/plugin/woocommerce-shipstation-integration/).

== Changelog ==

= 4.7.6 - 2025-08-11 =
* Tweak - WooCommerce 10.1 compatibility.

= 4.7.5 - 2025-08-05 =
* Fix   - Out of memory allocation error on checkout page.
* Fix   - Remove deprecated load_plugin_textdomain() call.

= 4.7.4 - 2025-07-07 =
* Tweak - WooCommerce 10.0 compatibility.

= 4.7.3 - 2025-06-30 =
* Fix   - Compatibility issue with WooCommerce version lower than 8.9.

= 4.7.2 - 2025-06-24 =
* Fix   - Fatal error on Checkout page.

= 4.7.1 - 2025-06-18 =
* Fix   - Fatal error on WooCommerce Subscriptions edit page.

= 4.7.0 - 2025-06-17 =
* Add   - REST API endpoints to update and retrieve product inventory data.
* Add   - Gift feature.

= 4.6.1 - 2025-06-09 =
* Tweak - WooCommerce 9.9 compatibility.

= 4.6.0 - 2025-06-02 =
* Add   - New hook `woocommerce_shipstation_shipnotify_status_updated` that will be called after the order status is changed.
* Add   - REST API endpoints to update and retrieve product inventory data.

= 4.5.2 - 2025-05-26 =
* Fix   - Security updates.
* Tweak - Update ShipStation branding.

= 4.5.1 - 2025-04-22 =
* Add   - Include the product dimensions when exporting an order to ShipStation.
* Tweak - Added a filter to allow the user to disable exporting order discounts as a separate line item to ShipStation.

= 4.5.0 - 2025-04-14 =
* Add   - woocommerce_shipstation_shipnotify_order_shipped filter - Allow to override is order shipped.
* Add   - woocommerce_shipstation_shipnotify_tracking_note filter - Allow to override tracking note.
* Add   - woocommerce_shipstation_shipnotify_send_tracking_note filter - Allow to override should tracking note be sent to customer.
* Tweak - Move woocommerce_shipstation_shipnotify action before order status is updated.

= 4.4.9 - 2025-04-07 =
* Tweak - WooCommerce 9.8 compatibility.

= 4.4.8 - 2025-03-10 =
* Fix   - Make the value of `woocommerce_shipstation_get_order_id` filter consistent by removing the conversion function.

= 4.4.7 - 2025-03-04 =
* Tweak - PHP 8.4 Compatibility.
* Tweak - WooCommerce 9.7 Compatibility.

= 4.4.6 - 2024-11-27 =
* Tweak - Reimplemented compatibility with WordPress 6.7 while maintaining unchanged execution priorities.

= 4.4.5 - 2024-10-28 =
* Tweak - WordPress 6.7 Compatibility.

= 4.4.4 - 2024-07-02 =
* Fix   - Security updates.
* Tweak - WooCommerce 9.0 and WordPress 6.6 Compatibility.

= 4.4.3 - 2024-05-27 =
* Tweak - Performance enhancement.

= 4.4.2 - 2024-04-09 =
* Fix - Cannot retrieve order number on from GET variable.

= 4.4.1 - 2024-03-25 =
* Tweak - WordPress 6.5 compatibility.

= 4.4.0 - 2024-03-19 =
* Fix - Applying WordPress coding standards.

= 4.3.9 - 2023-09-05 =
* Fix - Security updates.
* Tweaks - Developer dependencies update.
* Add - Developer QIT workflow.

= 4.3.8 - 2023-08-09 =
* Fix - Security updates.

= 4.3.7 - 2023-05-08 =
* Fix - Allow filtering the order exchange rate and currency code before exporting to ShipStation.

= 4.3.6 - 2023-04-20 =
* Fix - Compatibility for Sequential Order Numbers by WebToffee.
* Add - New query var for WC_Order_Query called `wt_order_number` to search order number.

= 4.3.5 - 2023-04-17 =
* Fix - Revert version 4.3.4's compatibility update for Sequential Order Numbers by WebToffee.

= 4.3.4 - 2023-04-12 =
* Fix   - Compatibility for Sequential Order Numbers by WebToffee.

= 4.3.3 - 2023-03-29 =
* Fix   - Fatal error when product image does not exist.

= 4.3.2 - 2022-11-29 =
* Fix   - Use product variation name when exporting a product variation.

= 4.3.1 - 2022-10-25 =
* Add   - Declared HPOS compatibility.

= 4.3.0 - 2022-10-13 =
* Add   - High-Performance Order Storage compatibility.

= 4.2.0 - 2022-09-07 =
* Add   - Filter for manipulating address export data.
* Fix   - Remove unnecessary files from plugin zip file.
* Tweak - Transition version numbering to WordPress versioning.
* Tweak - WC 6.7.0 and WP 6.0.1 compatibility.
* Fix - Remove 'translate : true' in package.json.

= 4.1.48 - 2021-11-03 =
* Fix - Critical Error when null value is passed to appendChild method.
* Fix - $logging_enabled compared against string instead of boolean.

= 4.1.47 - 2021-09-29 =
* Fix - Change API Export order search to be accurate down to the second, not just the date.

= 4.1.46 - 2021-09-10 =
* Fix   - Order is not changed to completed when the order has partial refund and is marked as shipped in ShipStation.

= 4.1.45 - 2021-08-24 =
* Fix    - Remove all usage of deprecated $HTTP_RAW_POST_DATA.

= 4.1.44 - 2021-08-12 =
* Fix    - Changing text domain to "woocommerce-shipstation-integration" to match with plugin slug.
* Fix    - Order product quantities do not sync to Shipstation when using a refund.
* Fix    - PHP notice error "wc_cog_order_total_cost" was called incorrectly.

= 4.1.43 - 2021-07-27 =
* Fix   - API returns status code 200 even when errors exist.
* Tweak - Add version compare for deprecated Order::get_product_from_item().

= 4.1.42 - 2021-04-20 =
* Fix - Use order currency code instead of store currency.

= 4.1.41 - 2021-03-02 =
* Add - Add currency code and weight units to orders XML.

= 4.1.40 - 2020-11-24 =
* Tweak - PHP 8 compatibility fixes.

= 4.1.39 - 2020-10-06 =
* Add   - Add woocommerce_shipstation_export_order_xml filter.
* Tweak - Update Readme.
* Tweak - WC 4.5 compatibility.
* Fix   - Updated shop_thumbnail to woocommerce_gallery_thumbnail for thumbnail export.

[See changelog for all versions](https://github.com/woocommerce/woocommerce-shipstation/raw/master/changelog.txt).
</file>

</files>
